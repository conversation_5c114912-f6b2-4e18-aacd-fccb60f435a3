import { Platform } from 'react-native';
import NetInfo from '@react-native-community/netinfo';
import * as Location from 'expo-location';

export interface NetworkInfo {
  isConnected: boolean;
  type: string;
  ssid: string;
  ipAddress: string;
  subnet: string;
  gateway: string;
}

// 获取真实的网络信息
export async function getEnhancedNetworkInfo(): Promise<NetworkInfo> {
  try {
    console.log('Getting enhanced network info...');
    
    // 首先获取基本网络信息
    const netInfo = await NetInfo.fetch();
    console.log('Basic NetInfo:', netInfo);
    
    let ssid = 'Unknown Network';
    
    // 尝试获取WiFi SSID
    if (Platform.OS === 'android' && netInfo.type === 'wifi') {
      try {
        // 请求位置权限（Android获取WiFi SSID需要位置权限）
        const { status } = await Location.requestForegroundPermissionsAsync();
        console.log('Location permission status:', status);
        
        if (status === 'granted') {
          // 重新获取网络信息，现在应该能获取到SSID
          const netInfoWithPermission = await NetInfo.fetch();
          console.log('NetInfo with permission:', netInfoWithPermission);
          
          if (netInfoWithPermission.details && 'ssid' in netInfoWithPermission.details) {
            ssid = netInfoWithPermission.details.ssid || 'Unknown Network';
          }
          
          // 如果还是获取不到，尝试其他方法
          if (ssid === 'Unknown Network' || ssid === null) {
            ssid = await getWifiSSIDAlternative();
          }
        } else {
          console.log('Location permission denied, cannot get WiFi SSID');
          ssid = 'Permission Required';
        }
      } catch (error) {
        console.error('Error getting WiFi SSID:', error);
        ssid = 'Error Getting SSID';
      }
    } else if (netInfo.type === 'wifi' && netInfo.details && 'ssid' in netInfo.details) {
      ssid = netInfo.details.ssid || 'Unknown Network';
    }
    
    const result = {
      isConnected: netInfo.isConnected || false,
      type: netInfo.type || 'unknown',
      ssid: ssid,
      ipAddress: netInfo.details?.ipAddress || '***********',
      subnet: netInfo.details?.subnet || '*************',
      gateway: netInfo.details?.gateway || '***********',
    };
    
    console.log('Final network info:', result);
    return result;
  } catch (error) {
    console.error('Error getting enhanced network info:', error);
    return {
      isConnected: false,
      type: 'unknown',
      ssid: 'Error',
      ipAddress: '***********',
      subnet: '*************',
      gateway: '***********',
    };
  }
}

// 尝试其他方法获取WiFi SSID
async function getWifiSSIDAlternative(): Promise<string> {
  try {
    // 在某些Android设备上，可能需要多次尝试
    for (let i = 0; i < 3; i++) {
      await new Promise(resolve => setTimeout(resolve, 500)); // 等待500ms
      
      const netInfo = await NetInfo.fetch();
      if (netInfo.details && 'ssid' in netInfo.details && netInfo.details.ssid) {
        console.log(`Got SSID on attempt ${i + 1}:`, netInfo.details.ssid);
        return netInfo.details.ssid;
      }
    }
    
    return 'WiFi Network';
  } catch (error) {
    console.error('Alternative SSID method failed:', error);
    return 'WiFi Network';
  }
}

// 从IP地址和子网掩码计算网段
export function getSubnetFromIP(ipAddress: string, subnet: string): string {
  try {
    const ipParts = ipAddress.split('.').map(Number);
    const subnetParts = subnet.split('.').map(Number);
    
    const networkParts = ipParts.map((ip, index) => ip & subnetParts[index]);
    
    // 返回网段的前三位（假设是/24网络）
    return `${networkParts[0]}.${networkParts[1]}.${networkParts[2]}`;
  } catch (error) {
    console.error('Error calculating subnet:', error);
    return '192.168.1';
  }
}
