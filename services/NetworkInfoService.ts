import { Platform } from 'react-native';
import NetInfo from '@react-native-community/netinfo';
import * as Location from 'expo-location';

export interface NetworkInfo {
  isConnected: boolean;
  type: string;
  ssid: string;
  ipAddress: string;
  subnet: string;
  gateway: string;
}

// 获取真实的网络信息
export async function getEnhancedNetworkInfo(): Promise<NetworkInfo> {
  try {
    console.log('🔍 Getting enhanced network info...');

    // 首先获取基本网络信息
    const netInfo = await NetInfo.fetch();
    console.log('📡 Basic NetInfo:', JSON.stringify(netInfo, null, 2));

    let ssid = 'Unknown Network';

    // 尝试获取WiFi SSID
    if (netInfo.type === 'wifi') {
      console.log('📶 WiFi detected, attempting to get SSID...');

      // 先检查是否已经有SSID信息
      if (netInfo.details && 'ssid' in netInfo.details && netInfo.details.ssid) {
        ssid = netInfo.details.ssid;
        console.log('✅ Got SSID directly:', ssid);
      } else {
        console.log('⚠️ No SSID in basic info, requesting location permission...');

        try {
          // 请求位置权限（Android获取WiFi SSID需要位置权限）
          const permissionResult = await Location.requestForegroundPermissionsAsync();
          console.log('📍 Location permission result:', permissionResult);

          if (permissionResult.status === 'granted') {
            console.log('✅ Location permission granted, retrying NetInfo...');

            // 等待一下再重新获取
            await new Promise(resolve => setTimeout(resolve, 1000));

            // 重新获取网络信息，现在应该能获取到SSID
            const netInfoWithPermission = await NetInfo.fetch();
            console.log('📡 NetInfo with permission:', JSON.stringify(netInfoWithPermission, null, 2));

            if (netInfoWithPermission.details && 'ssid' in netInfoWithPermission.details && netInfoWithPermission.details.ssid) {
              ssid = netInfoWithPermission.details.ssid;
              console.log('✅ Got SSID with permission:', ssid);
            } else {
              console.log('⚠️ Still no SSID, trying alternative methods...');
              ssid = await getWifiSSIDAlternative();
            }
          } else {
            console.log('❌ Location permission denied, status:', permissionResult.status);
            ssid = 'Permission Required - Please enable location access';
          }
        } catch (permissionError) {
          console.error('❌ Error requesting location permission:', permissionError);
          ssid = 'Permission Error';
        }
      }
    } else {
      console.log('📱 Not on WiFi, network type:', netInfo.type);
      ssid = netInfo.type === 'cellular' ? 'Mobile Data' : 'Not Connected';
    }

    const result = {
      isConnected: netInfo.isConnected || false,
      type: netInfo.type || 'unknown',
      ssid: ssid,
      ipAddress: netInfo.details?.ipAddress || '***********',
      subnet: netInfo.details?.subnet || '*************',
      gateway: netInfo.details?.gateway || '***********',
    };

    console.log('🎯 Final network info:', JSON.stringify(result, null, 2));
    return result;
  } catch (error) {
    console.error('💥 Error getting enhanced network info:', error);
    return {
      isConnected: false,
      type: 'unknown',
      ssid: 'Error',
      ipAddress: '***********',
      subnet: '*************',
      gateway: '***********',
    };
  }
}

// 尝试其他方法获取WiFi SSID
async function getWifiSSIDAlternative(): Promise<string> {
  try {
    console.log('🔄 Trying alternative SSID methods...');

    // 方法1: 多次尝试获取NetInfo
    for (let i = 0; i < 5; i++) {
      console.log(`🔄 Attempt ${i + 1}/5 to get SSID...`);
      await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒

      const netInfo = await NetInfo.fetch();
      console.log(`📡 Attempt ${i + 1} NetInfo:`, JSON.stringify(netInfo.details, null, 2));

      if (netInfo.details && 'ssid' in netInfo.details && netInfo.details.ssid) {
        console.log(`✅ Got SSID on attempt ${i + 1}:`, netInfo.details.ssid);
        return netInfo.details.ssid;
      }
    }

    // 方法2: 检查权限状态
    const permissionStatus = await Location.getForegroundPermissionsAsync();
    console.log('📍 Current permission status:', permissionStatus);

    if (permissionStatus.status !== 'granted') {
      return 'Location Permission Required';
    }

    // 方法3: 基于网络信息推断
    const netInfo = await NetInfo.fetch();
    if (netInfo.details?.ipAddress) {
      const ip = netInfo.details.ipAddress;
      if (ip.startsWith('192.168.1.')) {
        return 'Home WiFi Network';
      } else if (ip.startsWith('192.168.0.')) {
        return 'WiFi Network';
      } else if (ip.startsWith('10.')) {
        return 'Corporate WiFi';
      }
    }

    return 'WiFi Network';
  } catch (error) {
    console.error('❌ Alternative SSID method failed:', error);
    return 'WiFi Network';
  }
}

// 检查并请求所有必要的权限
export async function checkAndRequestPermissions(): Promise<{
  location: boolean;
  message: string;
}> {
  try {
    console.log('🔐 Checking permissions...');

    // 检查位置权限
    const locationStatus = await Location.getForegroundPermissionsAsync();
    console.log('📍 Location permission status:', locationStatus);

    if (locationStatus.status !== 'granted') {
      console.log('📍 Requesting location permission...');
      const requestResult = await Location.requestForegroundPermissionsAsync();
      console.log('📍 Location permission request result:', requestResult);

      if (requestResult.status === 'granted') {
        return {
          location: true,
          message: 'All permissions granted'
        };
      } else {
        return {
          location: false,
          message: 'Location permission is required to get WiFi network name. Please enable it in Settings.'
        };
      }
    }

    return {
      location: true,
      message: 'All permissions already granted'
    };
  } catch (error) {
    console.error('❌ Error checking permissions:', error);
    return {
      location: false,
      message: 'Error checking permissions'
    };
  }
}

// 从IP地址和子网掩码计算网段
export function getSubnetFromIP(ipAddress: string, subnet: string): string {
  try {
    const ipParts = ipAddress.split('.').map(Number);
    const subnetParts = subnet.split('.').map(Number);
    
    const networkParts = ipParts.map((ip, index) => ip & subnetParts[index]);
    
    // 返回网段的前三位（假设是/24网络）
    return `${networkParts[0]}.${networkParts[1]}.${networkParts[2]}`;
  } catch (error) {
    console.error('Error calculating subnet:', error);
    return '192.168.1';
  }
}
