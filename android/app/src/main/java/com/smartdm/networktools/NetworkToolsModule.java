package com.smartdm.networktools;

import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.bridge.WritableArray;
import com.facebook.react.bridge.Arguments;

import me.stealthcopter.networktools.SubnetDevices;
import me.stealthcopter.networktools.Ping;
import me.stealthcopter.networktools.PortScan;

import java.util.ArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import android.util.Log;

public class NetworkToolsModule extends ReactContextBaseJavaModule {
    
    private static final String MODULE_NAME = "AndroidNetworkTools";
    private static final String TAG = "NetworkTools";
    private ExecutorService executor = Executors.newCachedThreadPool();
    
    public NetworkToolsModule(ReactApplicationContext reactContext) {
        super(reactContext);
    }
    
    @Override
    public String getName() {
        return MODULE_NAME;
    }
    
    @ReactMethod
    public void isAvailable(Promise promise) {
        try {
            Log.d(TAG, "NetworkTools module is available");
            promise.resolve(true);
        } catch (Exception e) {
            Log.e(TAG, "NetworkTools module test failed", e);
            promise.reject("MODULE_ERROR", "NetworkTools module not available: " + e.getMessage());
        }
    }
    
    @ReactMethod
    public void scanSubnetDevices(Promise promise) {
        executor.execute(() -> {
            try {
                Log.d(TAG, "Starting ARP subnet scan...");
                WritableArray devices = Arguments.createArray();
                
                SubnetDevices.fromLocalAddress().findDevices(new SubnetDevices.OnSubnetDeviceFound() {
                    @Override
                    public void onDeviceFound(SubnetDevices.Device device) {
                        Log.d(TAG, "Found device: " + device.ip + " - " + device.hostname);
                        
                        WritableMap deviceInfo = Arguments.createMap();
                        deviceInfo.putString("ip", device.ip);
                        deviceInfo.putString("hostname", device.hostname != null ? device.hostname : "Unknown");
                        deviceInfo.putString("mac", device.mac != null ? device.mac : "Unknown");
                        deviceInfo.putDouble("responseTime", device.time);
                        deviceInfo.putString("discoveryMethod", "ARP");
                        deviceInfo.putString("vendor", getMacVendor(device.mac));
                        deviceInfo.putString("type", inferDeviceType(device.hostname, device.mac));
                        
                        devices.pushMap(deviceInfo);
                    }
                    
                    @Override
                    public void onFinished(ArrayList<SubnetDevices.Device> devicesFound) {
                        Log.d(TAG, "ARP scan completed. Found " + devicesFound.size() + " devices");
                        promise.resolve(devices);
                    }
                });
                
            } catch (Exception e) {
                Log.e(TAG, "ARP scan failed", e);
                promise.reject("ARP_SCAN_ERROR", "ARP扫描失败: " + e.getMessage());
            }
        });
    }
    
    private String getMacVendor(String macAddress) {
        if (macAddress == null || macAddress.length() < 8 || "Unknown".equals(macAddress)) {
            return "Unknown";
        }
        
        try {
            String oui = macAddress.substring(0, 8).toUpperCase().replace(":", "");
            
            switch (oui) {
                case "001A2B": case "00259C": return "Cisco";
                case "001B78": case "002264": return "HP";
                case "001E10": case "002713": return "Huawei";
                case "001F5B": case "002332": return "Apple";
                case "001D25": case "002454": return "Samsung";
                case "001EC9": case "002618": return "Xiaomi";
                default: return "Unknown";
            }
        } catch (Exception e) {
            return "Unknown";
        }
    }
    
    private String inferDeviceType(String hostname, String mac) {
        if (hostname != null && !hostname.equals("Unknown")) {
            String lowerHostname = hostname.toLowerCase();
            
            if (lowerHostname.contains("router") || lowerHostname.contains("gateway")) {
                return "router";
            } else if (lowerHostname.contains("printer")) {
                return "printer";
            } else if (lowerHostname.contains("camera")) {
                return "camera";
            } else if (lowerHostname.contains("phone") || lowerHostname.contains("mobile")) {
                return "mobile";
            }
        }
        
        return "unknown";
    }
}