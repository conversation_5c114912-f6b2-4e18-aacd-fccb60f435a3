{"root": "/Users/<USER>/Documents/dev_workshop/smartdm", "reactNativePath": "/Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native", "dependencies": {"@react-native-async-storage/async-storage": {"root": "/Users/<USER>/Documents/dev_workshop/smartdm/node_modules/@react-native-async-storage/async-storage", "name": "@react-native-async-storage/async-storage", "platforms": {"android": {"sourceDir": "/Users/<USER>/Documents/dev_workshop/smartdm/node_modules/@react-native-async-storage/async-storage/android", "packageImportPath": "import com.reactnativecommunity.asyncstorage.AsyncStoragePackage;", "packageInstance": "new AsyncStoragePackage()", "buildTypes": [], "libraryName": "rnasyncstorage", "componentDescriptors": [], "cmakeListsPath": "/Users/<USER>/Documents/dev_workshop/smartdm/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "@react-native-community/netinfo": {"root": "/Users/<USER>/Documents/dev_workshop/smartdm/node_modules/@react-native-community/netinfo", "name": "@react-native-community/netinfo", "platforms": {"android": {"sourceDir": "/Users/<USER>/Documents/dev_workshop/smartdm/node_modules/@react-native-community/netinfo/android", "packageImportPath": "import com.reactnativecommunity.netinfo.NetInfoPackage;", "packageInstance": "new NetInfoPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "/Users/<USER>/Documents/dev_workshop/smartdm/node_modules/@react-native-community/netinfo/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "expo": {"root": "/Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo", "name": "expo", "platforms": {"android": {"sourceDir": "/Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo/android", "packageImportPath": "import expo.modules.ExpoModulesPackage;", "packageInstance": "new ExpoModulesPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "/Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "expo-snmp": {"root": "/Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-snmp", "name": "expo-snmp", "platforms": {"android": {"sourceDir": "/Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-snmp/android", "packageImportPath": "import com.example.smartdm.SNMPPackage;", "packageInstance": "new SNMPPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "/Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-snmp/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-gesture-handler": {"root": "/Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-gesture-handler", "name": "react-native-gesture-handler", "platforms": {"android": {"sourceDir": "/Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-gesture-handler/android", "packageImportPath": "import com.swmansion.gesturehandler.RNGestureHandlerPackage;", "packageInstance": "new RNGestureHandlerPackage()", "buildTypes": [], "libraryName": "rngesturehandler_codegen", "componentDescriptors": ["RNGestureHandlerRootViewComponentDescriptor", "RNGestureHandlerButtonComponentDescriptor"], "cmakeListsPath": "/Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-network-info": {"root": "/Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-network-info", "name": "react-native-network-info", "platforms": {"android": {"sourceDir": "/Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-network-info/android", "packageImportPath": "import com.pusherman.networkinfo.RNNetworkInfoPackage;", "packageInstance": "new RNNetworkInfoPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "/Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-network-info/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-reanimated": {"root": "/Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-reanimated", "name": "react-native-reanimated", "platforms": {"android": {"sourceDir": "/Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-reanimated/android", "packageImportPath": "import com.swmansion.reanimated.ReanimatedPackage;", "packageInstance": "new ReanimatedPackage()", "buildTypes": [], "libraryName": "rnreanimated", "componentDescriptors": [], "cmakeListsPath": "/Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-safe-area-context": {"root": "/Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-safe-area-context", "name": "react-native-safe-area-context", "platforms": {"android": {"sourceDir": "/Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-safe-area-context/android", "packageImportPath": "import com.th3rdwave.safeareacontext.SafeAreaContextPackage;", "packageInstance": "new SafeAreaContextPackage()", "buildTypes": [], "libraryName": "safeareacontext", "componentDescriptors": ["RNCSafeAreaProviderComponentDescriptor", "RNCSafeAreaViewComponentDescriptor"], "cmakeListsPath": "/Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-screens": {"root": "/Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-screens", "name": "react-native-screens", "platforms": {"android": {"sourceDir": "/Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-screens/android", "packageImportPath": "import com.swmansion.rnscreens.RNScreensPackage;", "packageInstance": "new RNScreensPackage()", "buildTypes": [], "libraryName": "rnscreens", "componentDescriptors": ["RNSFullWindowOverlayComponentDescriptor", "RNSScreenContainerComponentDescriptor", "RNSScreenNavigationContainerComponentDescriptor", "RNSScreenStackHeaderConfigComponentDescriptor", "RNSScreenStackHeaderSubviewComponentDescriptor", "RNSScreenStackComponentDescriptor", "RNSSearchBarComponentDescriptor", "RNSScreenComponentDescriptor", "RNSScreenFooterComponentDescriptor", "RNSScreenContentWrapperComponentDescriptor", "RNSModalScreenComponentDescriptor"], "cmakeListsPath": "/Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-svg": {"root": "/Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-svg", "name": "react-native-svg", "platforms": {"android": {"sourceDir": "/Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-svg/android", "packageImportPath": "import com.horcrux.svg.SvgPackage;", "packageInstance": "new SvgPackage()", "buildTypes": [], "libraryName": "rnsvg", "componentDescriptors": ["RNSVGCircleComponentDescriptor", "RNSVGClipPathComponentDescriptor", "RNSVGDefsComponentDescriptor", "RNSVGFeBlendComponentDescriptor", "RNSVGFeColorMatrixComponentDescriptor", "RNSVGFeCompositeComponentDescriptor", "RNSVGFeFloodComponentDescriptor", "RNSVGFeGaussianBlurComponentDescriptor", "RNSVGFeMergeComponentDescriptor", "RNSVGFeOffsetComponentDescriptor", "RNSVGFilterComponentDescriptor", "RNSVGEllipseComponentDescriptor", "RNSVGForeignObjectComponentDescriptor", "RNSVGGroupComponentDescriptor", "RNSVGImageComponentDescriptor", "RNSVGLinearGradientComponentDescriptor", "RNSVGLineComponentDescriptor", "RNSVGMarkerComponentDescriptor", "RNSVGMaskComponentDescriptor", "RNSVGPathComponentDescriptor", "RNSVGPatternComponentDescriptor", "RNSVGRadialGradientComponentDescriptor", "RNSVGRectComponentDescriptor", "RNSVGSvgViewAndroidComponentDescriptor", "RNSVGSymbolComponentDescriptor", "RNSVGTextComponentDescriptor", "RNSVGTextPathComponentDescriptor", "RNSVGTSpanComponentDescriptor", "RNSVGUseComponentDescriptor"], "cmakeListsPath": "/Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-svg/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-webview": {"root": "/Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-webview", "name": "react-native-webview", "platforms": {"android": {"sourceDir": "/Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-webview/android", "packageImportPath": "import com.reactnativecommunity.webview.RNCWebViewPackage;", "packageInstance": "new RNCWebViewPackage()", "buildTypes": [], "libraryName": "RNCWebViewSpec", "componentDescriptors": ["RNCWebViewComponentDescriptor"], "cmakeListsPath": "/Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-webview/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}}, "project": {"android": {"packageName": "com.smartdm", "sourceDir": "/Users/<USER>/Documents/dev_workshop/smartdm/android"}}}