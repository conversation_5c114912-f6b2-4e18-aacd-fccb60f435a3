#!/bin/bash

# 批量修复Expo模块的build.gradle文件
echo "🔧 开始修复Expo模块的build.gradle文件..."

# 查找所有包含classifier = 'sources'的build.gradle文件
find node_modules -name "build.gradle" -path "*/android/*" -exec grep -l "classifier = 'sources'" {} \; | while read file; do
    echo "修复文件: $file"

    # 替换 classifier = 'sources' 为 archiveClassifier = 'sources'
    sed -i '' "s/classifier = 'sources'/archiveClassifier = 'sources'/g" "$file"

    # 更新compileSdkVersion从30到35
    sed -i '' "s/compileSdkVersion safeExtGet(\"compileSdkVersion\", 30)/compileSdkVersion safeExtGet(\"compileSdkVersion\", 35)/g" "$file"

    # 更新minSdkVersion从21到24
    sed -i '' "s/minSdkVersion safeExtGet(\"minSdkVersion\", 21)/minSdkVersion safeExtGet(\"minSdkVersion\", 24)/g" "$file"

    # 更新targetSdkVersion从30到35
    sed -i '' "s/targetSdkVersion safeExtGet(\"targetSdkVersion\", 30)/targetSdkVersion safeExtGet(\"targetSdkVersion\", 35)/g" "$file"

    echo "✅ 已修复: $file"
done

# 修复maven插件问题
echo "🔧 修复maven插件问题..."
find node_modules -name "build.gradle" -path "*/android/*" -exec grep -l "apply plugin: 'maven'" {} \; | while read file; do
    echo "修复maven插件: $file"
    sed -i '' "s/apply plugin: 'maven'/apply plugin: 'maven-publish'/g" "$file"
    echo "✅ 已修复maven插件: $file"
done

echo "🎉 所有Expo模块修复完成！"
