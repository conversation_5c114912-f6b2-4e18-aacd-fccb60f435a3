// Expo配置插件 - AndroidNetworkTools
const { withDangerousMod, withPlugins } = require('@expo/config-plugins');
const fs = require('fs');
const path = require('path');

/**
 * 添加AndroidNetworkTools支持的Expo插件
 */
function withAndroidNetworkTools(config) {
  return withPlugins(config, [
    // 添加Android依赖
    withAndroidDependencies,
    // 注册原生模块
    withAndroidMainApplication,
    // 添加权限
    withAndroidPermissions,
  ]);
}

/**
 * 添加Android依赖到build.gradle
 */
function withAndroidDependencies(config) {
  return withDangerousMod(config, [
    'android',
    async (config) => {
      const buildGradlePath = path.join(
        config.modRequest.platformProjectRoot,
        'app',
        'build.gradle'
      );
      
      if (fs.existsSync(buildGradlePath)) {
        let buildGradleContent = fs.readFileSync(buildGradlePath, 'utf8');
        
        // 添加jitpack仓库
        if (!buildGradleContent.includes('maven { url "https://jitpack.io" }')) {
          buildGradleContent = buildGradleContent.replace(
            /repositories\s*{/,
            `repositories {
        maven { url "https://jitpack.io" }`
          );
        }
        
        // 添加依赖
        const dependencies = `
    implementation 'com.github.stealthcopter:AndroidNetworkTools:0.4.5.3'
    implementation 'org.snmp4j:snmp4j:3.7.7'
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'`;
        
        if (!buildGradleContent.includes('AndroidNetworkTools')) {
          buildGradleContent = buildGradleContent.replace(
            /dependencies\s*{/,
            `dependencies {${dependencies}`
          );
        }
        
        fs.writeFileSync(buildGradlePath, buildGradleContent);
        console.log('✅ 已添加AndroidNetworkTools依赖到build.gradle');
      }
      
      return config;
    },
  ]);
}

/**
 * 注册原生模块到MainApplication.java
 */
function withAndroidMainApplication(config) {
  return withDangerousMod(config, [
    'android',
    async (config) => {
      // 查找MainApplication.java文件
      const possiblePaths = [
        path.join(config.modRequest.platformProjectRoot, 'app', 'src', 'main', 'java', 'com', 'example', 'smartdm', 'MainApplication.java'),
        path.join(config.modRequest.platformProjectRoot, 'app', 'src', 'main', 'java', 'com', 'smartdm', 'MainApplication.java'),
      ];
      
      let mainApplicationPath = null;
      for (const possiblePath of possiblePaths) {
        if (fs.existsSync(possiblePath)) {
          mainApplicationPath = possiblePath;
          break;
        }
      }
      
      if (!mainApplicationPath) {
        // 如果找不到，创建我们自己的原生模块文件
        const androidSrcPath = path.join(
          config.modRequest.platformProjectRoot,
          'app',
          'src',
          'main',
          'java',
          'com',
          'smartdm',
          'networktools'
        );
        
        if (!fs.existsSync(androidSrcPath)) {
          fs.mkdirSync(androidSrcPath, { recursive: true });
        }
        
        // 创建NetworkToolsModule.java
        const moduleContent = `package com.smartdm.networktools;

import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.bridge.WritableArray;
import com.facebook.react.bridge.Arguments;

import me.stealthcopter.networktools.SubnetDevices;
import me.stealthcopter.networktools.Ping;
import me.stealthcopter.networktools.PortScan;

import java.util.ArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import android.util.Log;

public class NetworkToolsModule extends ReactContextBaseJavaModule {
    
    private static final String MODULE_NAME = "AndroidNetworkTools";
    private static final String TAG = "NetworkTools";
    private ExecutorService executor = Executors.newCachedThreadPool();
    
    public NetworkToolsModule(ReactApplicationContext reactContext) {
        super(reactContext);
    }
    
    @Override
    public String getName() {
        return MODULE_NAME;
    }
    
    @ReactMethod
    public void isAvailable(Promise promise) {
        try {
            Log.d(TAG, "NetworkTools module is available");
            promise.resolve(true);
        } catch (Exception e) {
            Log.e(TAG, "NetworkTools module test failed", e);
            promise.reject("MODULE_ERROR", "NetworkTools module not available: " + e.getMessage());
        }
    }
    
    @ReactMethod
    public void scanSubnetDevices(Promise promise) {
        executor.execute(() -> {
            try {
                Log.d(TAG, "Starting ARP subnet scan...");
                WritableArray devices = Arguments.createArray();
                
                SubnetDevices.fromLocalAddress().findDevices(new SubnetDevices.OnSubnetDeviceFound() {
                    @Override
                    public void onDeviceFound(SubnetDevices.Device device) {
                        Log.d(TAG, "Found device: " + device.ip + " - " + device.hostname);
                        
                        WritableMap deviceInfo = Arguments.createMap();
                        deviceInfo.putString("ip", device.ip);
                        deviceInfo.putString("hostname", device.hostname != null ? device.hostname : "Unknown");
                        deviceInfo.putString("mac", device.mac != null ? device.mac : "Unknown");
                        deviceInfo.putDouble("responseTime", device.time);
                        deviceInfo.putString("discoveryMethod", "ARP");
                        deviceInfo.putString("vendor", getMacVendor(device.mac));
                        deviceInfo.putString("type", inferDeviceType(device.hostname, device.mac));
                        
                        devices.pushMap(deviceInfo);
                    }
                    
                    @Override
                    public void onFinished(ArrayList<SubnetDevices.Device> devicesFound) {
                        Log.d(TAG, "ARP scan completed. Found " + devicesFound.size() + " devices");
                        promise.resolve(devices);
                    }
                });
                
            } catch (Exception e) {
                Log.e(TAG, "ARP scan failed", e);
                promise.reject("ARP_SCAN_ERROR", "ARP扫描失败: " + e.getMessage());
            }
        });
    }
    
    private String getMacVendor(String macAddress) {
        if (macAddress == null || macAddress.length() < 8 || "Unknown".equals(macAddress)) {
            return "Unknown";
        }
        
        try {
            String oui = macAddress.substring(0, 8).toUpperCase().replace(":", "");
            
            switch (oui) {
                case "001A2B": case "00259C": return "Cisco";
                case "001B78": case "002264": return "HP";
                case "001E10": case "002713": return "Huawei";
                case "001F5B": case "002332": return "Apple";
                case "001D25": case "002454": return "Samsung";
                case "001EC9": case "002618": return "Xiaomi";
                default: return "Unknown";
            }
        } catch (Exception e) {
            return "Unknown";
        }
    }
    
    private String inferDeviceType(String hostname, String mac) {
        if (hostname != null && !hostname.equals("Unknown")) {
            String lowerHostname = hostname.toLowerCase();
            
            if (lowerHostname.contains("router") || lowerHostname.contains("gateway")) {
                return "router";
            } else if (lowerHostname.contains("printer")) {
                return "printer";
            } else if (lowerHostname.contains("camera")) {
                return "camera";
            } else if (lowerHostname.contains("phone") || lowerHostname.contains("mobile")) {
                return "mobile";
            }
        }
        
        return "unknown";
    }
}`;
        
        fs.writeFileSync(path.join(androidSrcPath, 'NetworkToolsModule.java'), moduleContent);
        
        // 创建NetworkToolsPackage.java
        const packageContent = `package com.smartdm.networktools;

import com.facebook.react.ReactPackage;
import com.facebook.react.bridge.NativeModule;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.uimanager.ViewManager;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class NetworkToolsPackage implements ReactPackage {
    
    @Override
    public List<NativeModule> createNativeModules(ReactApplicationContext reactContext) {
        List<NativeModule> modules = new ArrayList<>();
        modules.add(new NetworkToolsModule(reactContext));
        return modules;
    }
    
    @Override
    public List<ViewManager> createViewManagers(ReactApplicationContext reactContext) {
        return Collections.emptyList();
    }
}`;
        
        fs.writeFileSync(path.join(androidSrcPath, 'NetworkToolsPackage.java'), packageContent);
        
        console.log('✅ 已创建AndroidNetworkTools原生模块文件');
      }
      
      return config;
    },
  ]);
}

/**
 * 添加Android权限 - 权限已在app.json中配置，这里跳过
 */
function withAndroidPermissions(config) {
  // 权限已在app.json中配置，不需要在这里重复添加
  console.log('✅ 网络权限已在app.json中配置');
  return config;
}

module.exports = withAndroidNetworkTools;
