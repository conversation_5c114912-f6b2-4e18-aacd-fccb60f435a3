import React, { useState, useEffect } from 'react';
import { ScrollView, StyleSheet, Text, View, TouchableOpacity } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { Camera, Cpu, Wifi, CircleAlert as AlertCircle, Package, CircleHelp as HelpCircle } from 'lucide-react-native';
import DeviceGridCard from '@/components/DeviceGridCard';
import QuickActionButton from '@/components/QuickActionButton';
import SectionHeader from '@/components/SectionHeader';
import StatusBanner from '@/components/StatusBanner';
import { getNetworkInfo } from '@/services/SNMPService';

export default function HomeScreen() {
  const router = useRouter();
  const [networkInfo, setNetworkInfo] = useState({
    isConnected: false,
    ssid: 'Unknown Network',
    ipAddress: '***********'
  });

  useEffect(() => {
    // 获取真实的网络信息
    const fetchNetworkInfo = async () => {
      try {
        const info = await getNetworkInfo();
        setNetworkInfo(info);
        console.log('Network info loaded:', info);
      } catch (error) {
        console.error('Failed to get network info:', error);
      }
    };

    fetchNetworkInfo();
  }, []);

  return (
    <>
      <Stack.Screen 
        options={{
          title: '智能设备管理',
          headerLargeTitle: true,
          headerTransparent: false,
          headerStyle: {
            backgroundColor: '#7B68EE',
          },
          headerTintColor: '#FFFFFF',
          headerSearchBarOptions: {
            placeholder: '搜索设备',
          },
        }}
      />
      <ScrollView style={styles.container}>
        <StatusBanner
          title={networkInfo.isConnected ? `已连接到 ${networkInfo.ssid}` : '网络未连接'}
          subtitle={networkInfo.isConnected ? `IP: ${networkInfo.ipAddress} - 点击扫描局域网内设备` : '请检查网络连接'}
          type={networkInfo.isConnected ? "success" : "error"}
          icon={<Wifi size={20} color="#FFF" />}
        />
        
        <SectionHeader title="快捷功能" />
        <View style={styles.quickActions}>
          <QuickActionButton 
            icon={<Camera size={24} color="#7B68EE" />} 
            label="扫码识别"
            onPress={() => router.push('/scanner')}
          />
          <QuickActionButton 
            icon={<Cpu size={24} color="#20B2AA" />} 
            label="设备监控"
            onPress={() => router.push('/monitor')}
          />
          <QuickActionButton 
            icon={<AlertCircle size={24} color="#FF7F50" />} 
            label="智能诊断"
            onPress={() => router.push('/diagnosis')}
          />
          <QuickActionButton 
            icon={<Package size={24} color="#FFC107" />} 
            label="商品推荐"
            onPress={() => router.push('/products')}
          />
        </View>
        
        <SectionHeader 
          title="本地设备" 
          action={{
            label: "全部",
            onPress: () => router.push('/devices')
          }}
        />
        
        <View style={styles.deviceGrid}>
          <DeviceGridCard 
            deviceName="智能打印机"
            deviceModel="HP LaserJet Pro M428fdw"
            status="正常"
            statusColor="#4CAF50"
            onPress={() => router.push('/devices/printer-001')}
          />
          <DeviceGridCard 
            deviceName="网络路由器"
            deviceModel="华为 AX3 Pro"
            status="注意"
            statusColor="#FFC107"
            onPress={() => router.push('/devices/router-001')}
          />
          <DeviceGridCard 
            deviceName="智能摄像头"
            deviceModel="小米 智能摄像机 2K Pro"
            status="离线"
            statusColor="#F44336"
            onPress={() => router.push('/devices/camera-001')}
          />
          <DeviceGridCard 
            deviceName="智能电视"
            deviceModel="TCL 55V8-PRO"
            status="正常"
            statusColor="#4CAF50"
            onPress={() => router.push('/devices/tv-001')}
          />
        </View>
        
        <SectionHeader title="常见问题" />
        <View style={styles.helpSection}>
          <TouchableOpacity style={styles.helpItem} onPress={() => router.push('/faq/connect')}>
            <HelpCircle size={20} color="#7B68EE" />
            <Text style={styles.helpText}>如何连接新设备？</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.helpItem} onPress={() => router.push('/faq/troubleshoot')}>
            <HelpCircle size={20} color="#7B68EE" />
            <Text style={styles.helpText}>设备无法连接怎么办？</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.helpItem} onPress={() => router.push('/faq/update')}>
            <HelpCircle size={20} color="#7B68EE" />
            <Text style={styles.helpText}>如何更新设备固件？</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7F9FC',
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  deviceGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  helpSection: {
    backgroundColor: 'white',
    borderRadius: 12,
    marginHorizontal: 16,
    marginBottom: 24,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  helpItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  helpText: {
    marginLeft: 12,
    fontSize: 15,
    color: '#333',
  },
});