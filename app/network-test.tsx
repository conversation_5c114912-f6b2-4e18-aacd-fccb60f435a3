// 网络工具测试页面
import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Stack } from 'expo-router';
import NetworkToolsTest from '../components/NetworkToolsTest';

export default function NetworkTestScreen() {
  return (
    <View style={styles.container}>
      <Stack.Screen 
        options={{
          title: '网络工具测试',
          headerStyle: {
            backgroundColor: '#1890ff',
          },
          headerTintColor: '#fff',
          headerTitleStyle: {
            fontWeight: 'bold',
          },
        }} 
      />
      <NetworkToolsTest />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
});
