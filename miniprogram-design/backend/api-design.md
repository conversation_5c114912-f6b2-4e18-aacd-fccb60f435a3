# 微信小程序后端API设计

## 1. 设备信息相关API

### 1.1 二维码查询设备信息
```
POST /api/device/query
Content-Type: application/json

Request:
{
  "qrCode": "string",
  "timestamp": "number"
}

Response:
{
  "success": true,
  "data": {
    "id": "device_001",
    "name": "智能路由器 Pro",
    "model": "SR-2000",
    "brand": "SmartNet",
    "category": "网络设备",
    "image": "https://cdn.smartdm.com/devices/sr2000.jpg",
    "manualUrl": "https://cdn.smartdm.com/manuals/sr2000.pdf",
    "servicePhone": "************",
    "specifications": {
      "cpu": "双核 1.2GHz",
      "memory": "256MB",
      "wifi": "Wi-Fi 6",
      "ports": "4个千兆端口"
    },
    "features": ["智能QoS", "家长控制", "访客网络"],
    "warranty": "3年质保",
    "purchaseDate": "2023-01-15"
  }
}
```

### 1.2 获取设备详细信息
```
GET /api/device/detail?deviceId=device_001

Response:
{
  "success": true,
  "data": {
    // 同上设备信息结构
    "relatedProducts": [
      {
        "id": "accessory_001",
        "name": "高增益天线",
        "price": 89.00,
        "image": "https://cdn.smartdm.com/products/antenna.jpg"
      }
    ],
    "commonIssues": [
      {
        "issue": "网络连接不稳定",
        "frequency": "高",
        "solutions": ["重启设备", "检查线路", "更新固件"]
      }
    ]
  }
}
```

## 2. AI诊断相关API

### 2.1 智能诊断
```
POST /api/ai/diagnosis
Content-Type: application/json
Authorization: Bearer {token}

Request:
{
  "deviceId": "device_001",
  "deviceModel": "SR-2000",
  "userInput": "设备经常断网",
  "conversationHistory": [
    {
      "type": "user",
      "content": "设备无法启动",
      "timestamp": "10:30:15"
    }
  ],
  "timestamp": 1703123456789
}

Response:
{
  "success": true,
  "data": {
    "content": "根据您的描述，设备经常断网可能有以下几个原因：\n\n1. 网络环境问题\n2. 设备过热\n3. 固件版本过旧\n\n建议您先尝试以下解决方案：",
    "suggestions": [
      {
        "id": "solution_001",
        "title": "检查网络环境",
        "description": "确认网线连接正常，检查上级网络设备",
        "priority": "high",
        "estimatedTime": "5分钟"
      },
      {
        "id": "solution_002", 
        "title": "重启设备",
        "description": "断电30秒后重新启动设备",
        "priority": "medium",
        "estimatedTime": "2分钟"
      }
    ],
    "diagnosisResult": {
      "problemType": "网络连接",
      "severity": "medium",
      "confidence": 0.85,
      "recommendedActions": ["检查网络", "重启设备", "联系客服"]
    },
    "relatedKnowledge": [
      {
        "title": "路由器常见故障排除",
        "url": "/knowledge/router-troubleshooting"
      }
    ]
  }
}
```

### 2.2 保存诊断结果
```
POST /api/diagnosis/save
Content-Type: application/json

Request:
{
  "deviceId": "device_001",
  "userId": "user_123",
  "result": {
    "problemType": "网络连接",
    "severity": "medium",
    "confidence": 0.85
  },
  "conversation": [...],
  "timestamp": "2023-12-21T10:30:00Z"
}

Response:
{
  "success": true,
  "data": {
    "diagnosisId": "diag_001",
    "saved": true
  }
}
```

## 3. 推荐系统API

### 3.1 获取产品推荐
```
GET /api/recommend/products?deviceId=device_001&category=网络设备

Response:
{
  "success": true,
  "data": {
    "newProducts": [
      {
        "id": "product_001",
        "name": "智能路由器 Pro Max",
        "price": 599.00,
        "originalPrice": 699.00,
        "discount": "限时优惠",
        "image": "https://cdn.smartdm.com/products/sr3000.jpg",
        "features": ["Wi-Fi 6E", "2.5G端口", "AI加速"],
        "rating": 4.8,
        "isNew": true
      }
    ],
    "accessories": [
      {
        "id": "accessory_001",
        "name": "高增益天线套装",
        "price": 89.00,
        "compatibility": "完全兼容",
        "image": "https://cdn.smartdm.com/accessories/antenna-set.jpg"
      }
    ],
    "replacementParts": [
      {
        "id": "part_001",
        "name": "电源适配器",
        "price": 45.00,
        "partNumber": "PA-SR2000",
        "inStock": true
      }
    ]
  }
}
```

## 4. 知识库API

### 4.1 搜索知识库
```
GET /api/knowledge/search?q=路由器设置&category=网络设备

Response:
{
  "success": true,
  "data": {
    "articles": [
      {
        "id": "kb_001",
        "title": "路由器初始设置指南",
        "summary": "详细介绍路由器的基本设置步骤",
        "category": "设置指南",
        "readTime": "5分钟",
        "views": 1250,
        "helpful": 95,
        "url": "/knowledge/router-setup-guide"
      }
    ],
    "videos": [
      {
        "id": "video_001",
        "title": "路由器设置视频教程",
        "duration": "8:30",
        "thumbnail": "https://cdn.smartdm.com/videos/router-setup-thumb.jpg",
        "url": "https://cdn.smartdm.com/videos/router-setup.mp4"
      }
    ],
    "faqs": [
      {
        "question": "如何重置路由器？",
        "answer": "长按Reset按钮10秒钟，直到指示灯闪烁",
        "helpful": 89
      }
    ]
  }
}
```

## 5. 用户相关API

### 5.1 微信登录
```
POST /api/auth/wechat-login
Content-Type: application/json

Request:
{
  "code": "wx_code_from_wechat",
  "userInfo": {
    "nickName": "用户昵称",
    "avatarUrl": "头像URL"
  }
}

Response:
{
  "success": true,
  "data": {
    "token": "jwt_token_here",
    "userId": "user_123",
    "profile": {
      "nickName": "用户昵称",
      "avatarUrl": "头像URL",
      "memberLevel": "普通会员"
    }
  }
}
```

## 6. Deepseek AI集成

### 6.1 AI服务配置
```javascript
// 后端集成Deepseek API
const deepseekConfig = {
  apiKey: process.env.DEEPSEEK_API_KEY,
  baseURL: 'https://api.deepseek.com/v1',
  model: 'deepseek-chat'
};

// 构建诊断提示词
function buildDiagnosisPrompt(deviceInfo, userInput, history) {
  return `
你是一个专业的设备诊断专家。

设备信息：
- 型号：${deviceInfo.model}
- 品牌：${deviceInfo.brand}
- 类别：${deviceInfo.category}

用户问题：${userInput}

对话历史：${JSON.stringify(history)}

请提供：
1. 问题分析
2. 可能原因
3. 解决建议（按优先级排序）
4. 预防措施

回复格式要求：
- 语言简洁专业
- 提供具体可操作的建议
- 如果问题复杂，建议联系专业技术支持
`;
}
```

## 7. 数据库设计

### 7.1 设备信息表
```sql
CREATE TABLE devices (
  id VARCHAR(50) PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  model VARCHAR(50) NOT NULL,
  brand VARCHAR(50) NOT NULL,
  category VARCHAR(30) NOT NULL,
  specifications JSON,
  manual_url VARCHAR(255),
  image_url VARCHAR(255),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 7.2 诊断记录表
```sql
CREATE TABLE diagnosis_records (
  id VARCHAR(50) PRIMARY KEY,
  device_id VARCHAR(50) NOT NULL,
  user_id VARCHAR(50) NOT NULL,
  problem_type VARCHAR(50),
  severity ENUM('low', 'medium', 'high'),
  confidence DECIMAL(3,2),
  conversation JSON,
  result JSON,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (device_id) REFERENCES devices(id)
);
```

这个设计方案充分利用了微信小程序的能力，同时通过云服务实现了智能诊断和推荐功能。
