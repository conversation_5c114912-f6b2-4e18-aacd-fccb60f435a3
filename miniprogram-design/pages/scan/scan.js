// 微信小程序 - 扫码页面
Page({
  data: {
    scanning: false,
    deviceInfo: null,
    errorMsg: ''
  },

  onLoad() {
    // 检查相机权限
    this.checkCameraPermission();
  },

  // 检查相机权限
  checkCameraPermission() {
    wx.getSetting({
      success: (res) => {
        if (!res.authSetting['scope.camera']) {
          wx.authorize({
            scope: 'scope.camera',
            success: () => {
              console.log('相机权限获取成功');
            },
            fail: () => {
              wx.showModal({
                title: '权限申请',
                content: '需要相机权限来扫描二维码',
                showCancel: false
              });
            }
          });
        }
      }
    });
  },

  // 开始扫码
  startScan() {
    this.setData({ scanning: true, errorMsg: '' });
    
    wx.scanCode({
      success: (res) => {
        console.log('扫码结果:', res.result);
        this.handleScanResult(res.result);
      },
      fail: (err) => {
        console.error('扫码失败:', err);
        this.setData({ 
          scanning: false,
          errorMsg: '扫码失败，请重试' 
        });
      }
    });
  },

  // 处理扫码结果
  async handleScanResult(qrCode) {
    wx.showLoading({ title: '查询设备信息...' });
    
    try {
      // 调用后端API查询设备信息
      const deviceInfo = await this.queryDeviceInfo(qrCode);
      
      if (deviceInfo) {
        // 跳转到设备详情页
        wx.navigateTo({
          url: `/pages/device/device?deviceId=${deviceInfo.id}`
        });
      } else {
        this.setData({
          errorMsg: '未找到设备信息，请检查二维码是否正确'
        });
      }
    } catch (error) {
      console.error('查询设备信息失败:', error);
      this.setData({
        errorMsg: '查询失败，请检查网络连接'
      });
    } finally {
      wx.hideLoading();
      this.setData({ scanning: false });
    }
  },

  // 查询设备信息
  queryDeviceInfo(qrCode) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: 'https://api.smartdm.com/device/query',
        method: 'POST',
        data: {
          qrCode: qrCode,
          timestamp: Date.now()
        },
        header: {
          'content-type': 'application/json'
        },
        success: (res) => {
          if (res.statusCode === 200 && res.data.success) {
            resolve(res.data.data);
          } else {
            resolve(null);
          }
        },
        fail: reject
      });
    });
  },

  // 手动输入设备编号
  manualInput() {
    wx.showModal({
      title: '手动输入',
      content: '请输入设备编号或型号',
      editable: true,
      placeholderText: '设备编号/型号',
      success: (res) => {
        if (res.confirm && res.content) {
          this.handleScanResult(res.content);
        }
      }
    });
  }
});
