// 微信小程序 - 设备详情页面
Page({
  data: {
    deviceId: '',
    deviceInfo: null,
    manualUrl: '',
    loading: true,
    activeTab: 'info', // info, manual, diagnosis
    diagnosisHistory: []
  },

  onLoad(options) {
    if (options.deviceId) {
      this.setData({ deviceId: options.deviceId });
      this.loadDeviceInfo();
    }
  },

  // 加载设备信息
  async loadDeviceInfo() {
    wx.showLoading({ title: '加载中...' });
    
    try {
      const deviceInfo = await this.fetchDeviceInfo(this.data.deviceId);
      this.setData({ 
        deviceInfo,
        manualUrl: deviceInfo.manualUrl || '',
        loading: false 
      });
      
      // 记录查询历史
      this.recordViewHistory(deviceInfo);
    } catch (error) {
      console.error('加载设备信息失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 获取设备信息
  fetchDeviceInfo(deviceId) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: 'https://api.smartdm.com/device/detail',
        method: 'GET',
        data: { deviceId },
        success: (res) => {
          if (res.statusCode === 200 && res.data.success) {
            resolve(res.data.data);
          } else {
            reject(new Error('获取设备信息失败'));
          }
        },
        fail: reject
      });
    });
  },

  // 切换标签页
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({ activeTab: tab });
    
    if (tab === 'diagnosis') {
      this.loadDiagnosisHistory();
    }
  },

  // 查看说明书
  viewManual() {
    if (this.data.manualUrl) {
      // 在小程序内打开PDF或跳转到H5页面
      wx.navigateTo({
        url: `/pages/manual/manual?url=${encodeURIComponent(this.data.manualUrl)}`
      });
    } else {
      wx.showToast({
        title: '暂无说明书',
        icon: 'none'
      });
    }
  },

  // 下载说明书
  downloadManual() {
    if (this.data.manualUrl) {
      wx.downloadFile({
        url: this.data.manualUrl,
        success: (res) => {
          if (res.statusCode === 200) {
            wx.openDocument({
              filePath: res.tempFilePath,
              success: () => {
                console.log('打开文档成功');
              }
            });
          }
        },
        fail: () => {
          wx.showToast({
            title: '下载失败',
            icon: 'error'
          });
        }
      });
    }
  },

  // 开始智能诊断
  startDiagnosis() {
    wx.navigateTo({
      url: `/pages/diagnosis/diagnosis?deviceId=${this.data.deviceId}&model=${this.data.deviceInfo.model}`
    });
  },

  // 查看推荐产品
  viewRecommendations() {
    wx.navigateTo({
      url: `/pages/recommend/recommend?deviceId=${this.data.deviceId}&category=${this.data.deviceInfo.category}`
    });
  },

  // 联系客服
  contactService() {
    wx.makePhoneCall({
      phoneNumber: this.data.deviceInfo.servicePhone || '************'
    });
  },

  // 分享设备信息
  onShareAppMessage() {
    return {
      title: `${this.data.deviceInfo.name} - 设备信息`,
      path: `/pages/device/device?deviceId=${this.data.deviceId}`,
      imageUrl: this.data.deviceInfo.image
    };
  },

  // 记录查看历史
  recordViewHistory(deviceInfo) {
    try {
      let history = wx.getStorageSync('deviceHistory') || [];
      
      // 移除重复项
      history = history.filter(item => item.id !== deviceInfo.id);
      
      // 添加到开头
      history.unshift({
        id: deviceInfo.id,
        name: deviceInfo.name,
        model: deviceInfo.model,
        image: deviceInfo.image,
        viewTime: new Date().toISOString()
      });
      
      // 只保留最近20条
      history = history.slice(0, 20);
      
      wx.setStorageSync('deviceHistory', history);
    } catch (error) {
      console.error('保存历史记录失败:', error);
    }
  },

  // 加载诊断历史
  loadDiagnosisHistory() {
    wx.request({
      url: 'https://api.smartdm.com/diagnosis/history',
      method: 'GET',
      data: { 
        deviceId: this.data.deviceId,
        userId: wx.getStorageSync('userId')
      },
      success: (res) => {
        if (res.statusCode === 200 && res.data.success) {
          this.setData({ diagnosisHistory: res.data.data });
        }
      }
    });
  }
});
