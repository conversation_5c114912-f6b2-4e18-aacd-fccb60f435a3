// 微信小程序 - 智能诊断页面
Page({
  data: {
    deviceId: '',
    deviceModel: '',
    messages: [],
    inputText: '',
    isTyping: false,
    quickQuestions: [
      '设备无法启动',
      '运行噪音过大',
      '性能下降',
      '温度过高',
      '连接问题'
    ],
    diagnosisResult: null
  },

  onLoad(options) {
    this.setData({
      deviceId: options.deviceId || '',
      deviceModel: options.model || ''
    });
    
    // 初始化对话
    this.initConversation();
  },

  // 初始化对话
  initConversation() {
    const welcomeMessage = {
      id: Date.now(),
      type: 'ai',
      content: `您好！我是智能诊断助手。我将帮助您诊断 ${this.data.deviceModel} 的问题。请描述您遇到的具体问题，或选择下方的常见问题。`,
      timestamp: new Date().toLocaleTimeString()
    };
    
    this.setData({
      messages: [welcomeMessage]
    });
  },

  // 发送消息
  async sendMessage(content = null) {
    const messageContent = content || this.data.inputText.trim();
    
    if (!messageContent) {
      wx.showToast({
        title: '请输入问题描述',
        icon: 'none'
      });
      return;
    }

    // 添加用户消息
    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: messageContent,
      timestamp: new Date().toLocaleTimeString()
    };

    this.setData({
      messages: [...this.data.messages, userMessage],
      inputText: '',
      isTyping: true
    });

    // 滚动到底部
    this.scrollToBottom();

    try {
      // 调用AI诊断服务
      const aiResponse = await this.callAIDiagnosis(messageContent);
      
      const aiMessage = {
        id: Date.now() + 1,
        type: 'ai',
        content: aiResponse.content,
        suggestions: aiResponse.suggestions || [],
        timestamp: new Date().toLocaleTimeString()
      };

      this.setData({
        messages: [...this.data.messages, aiMessage],
        isTyping: false
      });

      // 如果有诊断结果，保存
      if (aiResponse.diagnosisResult) {
        this.setData({ diagnosisResult: aiResponse.diagnosisResult });
        this.saveDiagnosisResult(aiResponse.diagnosisResult);
      }

    } catch (error) {
      console.error('AI诊断失败:', error);
      
      const errorMessage = {
        id: Date.now() + 1,
        type: 'ai',
        content: '抱歉，诊断服务暂时不可用，请稍后重试或联系客服。',
        timestamp: new Date().toLocaleTimeString()
      };

      this.setData({
        messages: [...this.data.messages, errorMessage],
        isTyping: false
      });
    }

    this.scrollToBottom();
  },

  // 调用AI诊断服务
  callAIDiagnosis(userInput) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: 'https://api.smartdm.com/ai/diagnosis',
        method: 'POST',
        data: {
          deviceId: this.data.deviceId,
          deviceModel: this.data.deviceModel,
          userInput: userInput,
          conversationHistory: this.data.messages.slice(-10), // 最近10条消息作为上下文
          timestamp: Date.now()
        },
        header: {
          'content-type': 'application/json',
          'Authorization': `Bearer ${wx.getStorageSync('token')}`
        },
        success: (res) => {
          if (res.statusCode === 200 && res.data.success) {
            resolve(res.data.data);
          } else {
            reject(new Error(res.data.message || '诊断失败'));
          }
        },
        fail: reject
      });
    });
  },

  // 快速问题点击
  onQuickQuestionTap(e) {
    const question = e.currentTarget.dataset.question;
    this.sendMessage(question);
  },

  // 输入框内容变化
  onInputChange(e) {
    this.setData({
      inputText: e.detail.value
    });
  },

  // 滚动到底部
  scrollToBottom() {
    setTimeout(() => {
      wx.pageScrollTo({
        scrollTop: 999999,
        duration: 300
      });
    }, 100);
  },

  // 保存诊断结果
  saveDiagnosisResult(result) {
    wx.request({
      url: 'https://api.smartdm.com/diagnosis/save',
      method: 'POST',
      data: {
        deviceId: this.data.deviceId,
        userId: wx.getStorageSync('userId'),
        result: result,
        conversation: this.data.messages,
        timestamp: new Date().toISOString()
      },
      success: (res) => {
        console.log('诊断结果已保存');
      }
    });
  },

  // 查看推荐解决方案
  viewSolution(e) {
    const solution = e.currentTarget.dataset.solution;
    wx.navigateTo({
      url: `/pages/solution/solution?id=${solution.id}`
    });
  },

  // 联系专家
  contactExpert() {
    wx.showModal({
      title: '联系专家',
      content: '是否需要人工专家进一步协助？',
      success: (res) => {
        if (res.confirm) {
          // 跳转到专家咨询页面或拨打电话
          wx.makePhoneCall({
            phoneNumber: '************'
          });
        }
      }
    });
  },

  // 分享诊断结果
  onShareAppMessage() {
    return {
      title: '设备智能诊断结果',
      path: `/pages/diagnosis/result?deviceId=${this.data.deviceId}`,
      imageUrl: '/images/diagnosis-share.png'
    };
  },

  // 清空对话
  clearConversation() {
    wx.showModal({
      title: '确认清空',
      content: '是否清空当前对话记录？',
      success: (res) => {
        if (res.confirm) {
          this.initConversation();
        }
      }
    });
  }
});
