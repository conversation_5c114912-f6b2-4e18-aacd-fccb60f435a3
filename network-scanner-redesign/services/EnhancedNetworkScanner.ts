// 增强的网络扫描服务
import { NativeModules, Platform } from 'react-native';
import { getEnhancedNetworkInfo } from './NetworkInfoService';

const { NetworkScannerModule } = NativeModules;

export interface EnhancedDeviceInfo {
  // 基础信息
  id: string;
  name: string;
  ipAddress: string;
  macAddress?: string;
  hostname?: string;
  vendor?: string;
  type: string;
  
  // 连接信息
  isReachable: boolean;
  responseTime?: number;
  discoveryMethod: 'ARP' | 'HTTP' | 'PING' | 'SNMP';
  
  // 网络服务
  openPorts: number[];
  services: Array<{
    port: number;
    service: string;
    banner?: string;
  }>;
  
  // SNMP信息
  snmpSupported: boolean;
  snmpCommunities?: string[];
  snmpData?: {
    systemInfo?: any;
    interfaces?: any[];
    performance?: any;
  };
  
  // 设备指纹
  httpBanner?: string;
  sshBanner?: string;
  detectedType?: string;
  
  // 元数据
  scanTime: string;
  lastSeen: string;
  confidence: number; // 设备类型识别置信度
}

export interface ScanProgress {
  phase: 'discovery' | 'port_scan' | 'snmp_check' | 'fingerprint' | 'complete';
  progress: number; // 0-100
  currentIP?: string;
  devicesFound: number;
  message: string;
}

/**
 * 增强的网络扫描器
 */
class EnhancedNetworkScanner {
  private scanInProgress = false;
  private onProgressCallback?: (progress: ScanProgress) => void;
  private onDeviceFoundCallback?: (device: EnhancedDeviceInfo) => void;

  /**
   * 检查原生模块是否可用
   */
  isAvailable(): boolean {
    return Platform.OS === 'android' && NetworkScannerModule !== null;
  }

  /**
   * 全面的网络设备扫描
   */
  async scanNetwork(options: {
    subnet?: string;
    enablePortScan?: boolean;
    enableSNMPCheck?: boolean;
    enableFingerprinting?: boolean;
    onProgress?: (progress: ScanProgress) => void;
    onDeviceFound?: (device: EnhancedDeviceInfo) => void;
  } = {}): Promise<EnhancedDeviceInfo[]> {
    
    if (this.scanInProgress) {
      throw new Error('扫描正在进行中，请等待完成');
    }

    this.scanInProgress = true;
    this.onProgressCallback = options.onProgress;
    this.onDeviceFoundCallback = options.onDeviceFound;

    try {
      console.log('🔍 开始增强网络扫描...');
      
      // 阶段1: 网络发现
      this.reportProgress({
        phase: 'discovery',
        progress: 0,
        devicesFound: 0,
        message: '正在发现网络设备...'
      });

      const networkInfo = await getEnhancedNetworkInfo();
      const subnet = options.subnet || this.calculateSubnet(networkInfo.ipAddress, networkInfo.subnet);
      
      console.log(`📡 扫描网段: ${subnet}`);

      // 使用ARP扫描发现设备
      const discoveredDevices = await this.performARPScan(subnet);
      
      this.reportProgress({
        phase: 'discovery',
        progress: 30,
        devicesFound: discoveredDevices.length,
        message: `发现 ${discoveredDevices.length} 个设备`
      });

      // 阶段2: 增强设备信息
      const enhancedDevices: EnhancedDeviceInfo[] = [];
      
      for (let i = 0; i < discoveredDevices.length; i++) {
        const device = discoveredDevices[i];
        
        this.reportProgress({
          phase: 'port_scan',
          progress: 30 + (i / discoveredDevices.length) * 40,
          currentIP: device.ipAddress,
          devicesFound: enhancedDevices.length,
          message: `正在扫描设备: ${device.ipAddress}`
        });

        try {
          const enhancedDevice = await this.enhanceDeviceInfo(device, options);
          enhancedDevices.push(enhancedDevice);
          
          // 实时回调新发现的设备
          this.onDeviceFoundCallback?.(enhancedDevice);
          
        } catch (error) {
          console.warn(`增强设备信息失败 ${device.ipAddress}:`, error);
          // 使用基础信息
          enhancedDevices.push(this.createBasicDeviceInfo(device));
        }
      }

      this.reportProgress({
        phase: 'complete',
        progress: 100,
        devicesFound: enhancedDevices.length,
        message: `扫描完成，共发现 ${enhancedDevices.length} 个设备`
      });

      console.log(`✅ 网络扫描完成，发现 ${enhancedDevices.length} 个设备`);
      return enhancedDevices;

    } finally {
      this.scanInProgress = false;
    }
  }

  /**
   * 执行ARP扫描
   */
  private async performARPScan(subnet: string): Promise<Partial<EnhancedDeviceInfo>[]> {
    if (!this.isAvailable()) {
      console.warn('原生扫描模块不可用，使用回退方案');
      return this.fallbackDiscovery(subnet);
    }

    try {
      const devices = await NetworkScannerModule.scanSubnetDevices(subnet);
      
      return devices.map((device: any) => ({
        ipAddress: device.ip,
        hostname: device.hostname,
        macAddress: device.mac,
        vendor: device.vendor,
        type: device.type,
        discoveryMethod: 'ARP' as const,
        responseTime: device.time,
        isReachable: true
      }));
      
    } catch (error) {
      console.error('ARP扫描失败:', error);
      return this.fallbackDiscovery(subnet);
    }
  }

  /**
   * 增强设备信息
   */
  private async enhanceDeviceInfo(
    basicDevice: Partial<EnhancedDeviceInfo>, 
    options: any
  ): Promise<EnhancedDeviceInfo> {
    
    if (!this.isAvailable()) {
      return this.createBasicDeviceInfo(basicDevice);
    }

    try {
      const enhancedInfo = await NetworkScannerModule.getEnhancedDeviceInfo(basicDevice.ipAddress);
      
      // 合并基础信息和增强信息
      const device: EnhancedDeviceInfo = {
        id: basicDevice.ipAddress!.replace(/\./g, '-'),
        name: this.generateDeviceName(basicDevice, enhancedInfo),
        ipAddress: basicDevice.ipAddress!,
        macAddress: basicDevice.macAddress,
        hostname: basicDevice.hostname,
        vendor: basicDevice.vendor,
        type: this.determineDeviceType(basicDevice, enhancedInfo),
        
        isReachable: enhancedInfo.isReachable || false,
        responseTime: enhancedInfo.responseTime,
        discoveryMethod: basicDevice.discoveryMethod || 'ARP',
        
        openPorts: enhancedInfo.openPorts || [],
        services: enhancedInfo.services || [],
        
        snmpSupported: enhancedInfo.snmpSupported || false,
        snmpCommunities: enhancedInfo.snmpCommunities,
        
        httpBanner: enhancedInfo.httpBanner,
        sshBanner: enhancedInfo.sshBanner,
        detectedType: enhancedInfo.detectedType,
        
        scanTime: new Date().toISOString(),
        lastSeen: new Date().toISOString(),
        confidence: this.calculateConfidence(basicDevice, enhancedInfo)
      };

      // 如果支持SNMP，获取SNMP数据
      if (device.snmpSupported && options.enableSNMPCheck) {
        device.snmpData = await this.getSNMPData(device.ipAddress, device.snmpCommunities?.[0]);
      }

      return device;
      
    } catch (error) {
      console.error(`增强设备信息失败 ${basicDevice.ipAddress}:`, error);
      return this.createBasicDeviceInfo(basicDevice);
    }
  }

  /**
   * 获取SNMP数据
   */
  private async getSNMPData(ipAddress: string, community?: string): Promise<any> {
    if (!community) community = 'public';
    
    try {
      // 这里集成真实的SNMP查询
      // 可以使用之前设计的SNMP模块
      return {
        systemInfo: {
          description: 'SNMP数据获取中...',
          uptime: '未知',
          name: '未知'
        },
        interfaces: [],
        performance: {
          cpu: 0,
          memory: 0,
          disk: 0,
          temperature: 0
        }
      };
    } catch (error) {
      console.error('SNMP数据获取失败:', error);
      return null;
    }
  }

  /**
   * 回退发现方案
   */
  private async fallbackDiscovery(subnet: string): Promise<Partial<EnhancedDeviceInfo>[]> {
    console.log('使用HTTP回退扫描方案');
    
    const devices: Partial<EnhancedDeviceInfo>[] = [];
    const commonIPs = [1, 2, 100, 101, 102, 200, 201, 254]; // 常见设备IP
    
    for (const lastOctet of commonIPs) {
      const ip = `${subnet}.${lastOctet}`;
      
      try {
        const isReachable = await this.checkHTTPReachability(ip);
        if (isReachable) {
          devices.push({
            ipAddress: ip,
            discoveryMethod: 'HTTP',
            isReachable: true,
            type: this.inferTypeFromIP(lastOctet)
          });
        }
      } catch (error) {
        // 忽略单个IP的错误
      }
    }
    
    return devices;
  }

  /**
   * HTTP可达性检查
   */
  private async checkHTTPReachability(ip: string): Promise<boolean> {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 2000);

      await fetch(`http://${ip}`, {
        method: 'HEAD',
        signal: controller.signal,
        mode: 'no-cors'
      });

      clearTimeout(timeoutId);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 生成设备名称
   */
  private generateDeviceName(basicDevice: Partial<EnhancedDeviceInfo>, enhancedInfo: any): string {
    if (basicDevice.hostname && basicDevice.hostname !== 'unknown') {
      return basicDevice.hostname;
    }
    
    if (basicDevice.vendor && basicDevice.vendor !== 'Unknown') {
      return `${basicDevice.vendor} ${basicDevice.type || 'Device'}`;
    }
    
    if (enhancedInfo.detectedType) {
      return `${enhancedInfo.detectedType} (${basicDevice.ipAddress})`;
    }
    
    return `Device ${basicDevice.ipAddress}`;
  }

  /**
   * 确定设备类型
   */
  private determineDeviceType(basicDevice: Partial<EnhancedDeviceInfo>, enhancedInfo: any): string {
    // 优先级: 端口分析 > 厂商推断 > 主机名分析 > IP推断
    
    if (enhancedInfo.services) {
      const services = enhancedInfo.services.map((s: any) => s.service);
      
      if (services.includes('SNMP') && services.includes('HTTP')) {
        return 'router';
      } else if (services.includes('Printer')) {
        return 'printer';
      } else if (services.includes('SSH') && services.includes('HTTP')) {
        return 'server';
      }
    }
    
    if (enhancedInfo.detectedType && enhancedInfo.detectedType !== 'unknown') {
      return enhancedInfo.detectedType;
    }
    
    if (basicDevice.type && basicDevice.type !== 'unknown') {
      return basicDevice.type;
    }
    
    return 'unknown';
  }

  /**
   * 计算识别置信度
   */
  private calculateConfidence(basicDevice: Partial<EnhancedDeviceInfo>, enhancedInfo: any): number {
    let confidence = 0;
    
    // 有MAC地址 +20
    if (basicDevice.macAddress) confidence += 20;
    
    // 有主机名 +20
    if (basicDevice.hostname && basicDevice.hostname !== 'unknown') confidence += 20;
    
    // 有厂商信息 +15
    if (basicDevice.vendor && basicDevice.vendor !== 'Unknown') confidence += 15;
    
    // 有开放端口 +15
    if (enhancedInfo.openPorts && enhancedInfo.openPorts.length > 0) confidence += 15;
    
    // 有服务识别 +20
    if (enhancedInfo.services && enhancedInfo.services.length > 0) confidence += 20;
    
    // 支持SNMP +10
    if (enhancedInfo.snmpSupported) confidence += 10;
    
    return Math.min(confidence, 100);
  }

  /**
   * 创建基础设备信息
   */
  private createBasicDeviceInfo(basicDevice: Partial<EnhancedDeviceInfo>): EnhancedDeviceInfo {
    return {
      id: basicDevice.ipAddress!.replace(/\./g, '-'),
      name: basicDevice.hostname || `Device ${basicDevice.ipAddress}`,
      ipAddress: basicDevice.ipAddress!,
      macAddress: basicDevice.macAddress,
      hostname: basicDevice.hostname,
      vendor: basicDevice.vendor,
      type: basicDevice.type || 'unknown',
      
      isReachable: basicDevice.isReachable || false,
      responseTime: basicDevice.responseTime,
      discoveryMethod: basicDevice.discoveryMethod || 'HTTP',
      
      openPorts: [],
      services: [],
      
      snmpSupported: false,
      
      scanTime: new Date().toISOString(),
      lastSeen: new Date().toISOString(),
      confidence: 30 // 基础置信度
    };
  }

  /**
   * 报告扫描进度
   */
  private reportProgress(progress: ScanProgress) {
    console.log(`📊 扫描进度: ${progress.phase} - ${progress.progress}% - ${progress.message}`);
    this.onProgressCallback?.(progress);
  }

  /**
   * 计算子网
   */
  private calculateSubnet(ipAddress: string, subnet: string): string {
    const ipParts = ipAddress.split('.').map(Number);
    const subnetParts = subnet.split('.').map(Number);
    
    const networkParts = ipParts.map((ip, index) => ip & subnetParts[index]);
    
    return `${networkParts[0]}.${networkParts[1]}.${networkParts[2]}`;
  }

  /**
   * 根据IP推断设备类型
   */
  private inferTypeFromIP(lastOctet: number): string {
    if (lastOctet === 1) return 'router';
    if (lastOctet >= 2 && lastOctet <= 10) return 'router';
    if (lastOctet >= 100 && lastOctet <= 150) return 'printer';
    if (lastOctet >= 200) return 'computer';
    return 'unknown';
  }
}

export default new EnhancedNetworkScanner();
