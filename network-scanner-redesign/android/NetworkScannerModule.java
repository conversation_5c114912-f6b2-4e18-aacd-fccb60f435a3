// Android原生网络扫描模块
package com.smartdm.networkscanner;

import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.bridge.WritableArray;
import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.ReadableMap;

import me.stealthcopter.networktools.ARPInfo;
import me.stealthcopter.networktools.SubnetDevices;
import me.stealthcopter.networktools.Ping;
import me.stealthcopter.networktools.PortScan;

import java.net.InetAddress;
import java.util.ArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class NetworkScannerModule extends ReactContextBaseJavaModule {
    
    private static final String MODULE_NAME = "NetworkScannerModule";
    private ExecutorService executor = Executors.newCachedThreadPool();
    
    public NetworkScannerModule(ReactApplicationContext reactContext) {
        super(reactContext);
    }
    
    @Override
    public String getName() {
        return MODULE_NAME;
    }
    
    /**
     * ARP扫描发现局域网设备
     */
    @ReactMethod
    public void scanSubnetDevices(String subnet, Promise promise) {
        executor.execute(() -> {
            try {
                WritableArray devices = Arguments.createArray();
                
                // 使用AndroidNetworkTools进行ARP扫描
                SubnetDevices.fromLocalAddress().findDevices(new SubnetDevices.OnSubnetDeviceFound() {
                    @Override
                    public void onDeviceFound(Device device) {
                        WritableMap deviceInfo = Arguments.createMap();
                        
                        deviceInfo.putString("ip", device.ip);
                        deviceInfo.putString("hostname", device.hostname);
                        deviceInfo.putString("mac", device.mac);
                        deviceInfo.putDouble("time", device.time);
                        deviceInfo.putString("discoveryMethod", "ARP");
                        
                        // 获取设备厂商信息
                        String vendor = getMacVendor(device.mac);
                        deviceInfo.putString("vendor", vendor);
                        
                        // 推断设备类型
                        String deviceType = inferDeviceType(device.hostname, device.mac, vendor);
                        deviceInfo.putString("type", deviceType);
                        
                        devices.pushMap(deviceInfo);
                    }
                    
                    @Override
                    public void onFinished(ArrayList<Device> devicesFound) {
                        promise.resolve(devices);
                    }
                });
                
            } catch (Exception e) {
                promise.reject("ARP_SCAN_ERROR", "ARP扫描失败: " + e.getMessage());
            }
        });
    }
    
    /**
     * 增强设备信息获取
     */
    @ReactMethod
    public void getEnhancedDeviceInfo(String ipAddress, Promise promise) {
        executor.execute(() -> {
            try {
                WritableMap deviceInfo = Arguments.createMap();
                
                // 基础信息
                deviceInfo.putString("ip", ipAddress);
                deviceInfo.putString("scanTime", String.valueOf(System.currentTimeMillis()));
                
                // Ping测试
                Ping.onAddress(ipAddress).setTimeOutMillis(2000).doPing(new Ping.PingListener() {
                    @Override
                    public void onResult(PingResult pingResult) {
                        deviceInfo.putBoolean("isReachable", pingResult.isReachable);
                        deviceInfo.putDouble("responseTime", pingResult.timeTaken);
                        deviceInfo.putString("error", pingResult.error);
                    }
                });
                
                // 端口扫描
                scanCommonPorts(ipAddress, deviceInfo);
                
                // SNMP检测
                checkSNMPSupport(ipAddress, deviceInfo);
                
                // 设备指纹识别
                performDeviceFingerprinting(ipAddress, deviceInfo);
                
                promise.resolve(deviceInfo);
                
            } catch (Exception e) {
                promise.reject("DEVICE_INFO_ERROR", "获取设备信息失败: " + e.getMessage());
            }
        });
    }
    
    /**
     * 扫描常见端口
     */
    private void scanCommonPorts(String ipAddress, WritableMap deviceInfo) {
        int[] commonPorts = {21, 22, 23, 25, 53, 80, 110, 143, 161, 443, 993, 995, 8080, 9100};
        WritableArray openPorts = Arguments.createArray();
        WritableArray services = Arguments.createArray();
        
        for (int port : commonPorts) {
            PortScan.onAddress(ipAddress).setPort(port).setTimeOutMillis(1000).doScan(new PortScan.PortListener() {
                @Override
                public void onResult(PortResult portResult) {
                    if (portResult.isOpen()) {
                        openPorts.pushInt(port);
                        
                        // 识别服务类型
                        String service = identifyService(port);
                        if (service != null) {
                            WritableMap serviceInfo = Arguments.createMap();
                            serviceInfo.putInt("port", port);
                            serviceInfo.putString("service", service);
                            services.pushMap(serviceInfo);
                        }
                    }
                }
            });
        }
        
        deviceInfo.putArray("openPorts", openPorts);
        deviceInfo.putArray("services", services);
    }
    
    /**
     * 检测SNMP支持
     */
    private void checkSNMPSupport(String ipAddress, WritableMap deviceInfo) {
        // 检测161端口是否开放
        PortScan.onAddress(ipAddress).setPort(161).setTimeOutMillis(2000).doScan(new PortScan.PortListener() {
            @Override
            public void onResult(PortResult portResult) {
                boolean snmpPortOpen = portResult.isOpen();
                deviceInfo.putBoolean("snmpPortOpen", snmpPortOpen);
                
                if (snmpPortOpen) {
                    // 尝试SNMP查询
                    testSNMPCommunities(ipAddress, deviceInfo);
                }
            }
        });
    }
    
    /**
     * 测试SNMP社区字符串
     */
    private void testSNMPCommunities(String ipAddress, WritableMap deviceInfo) {
        String[] communities = {"public", "private", "admin", "manager", "monitor"};
        WritableArray validCommunities = Arguments.createArray();
        
        for (String community : communities) {
            try {
                // 这里需要集成SNMP4J库进行真实的SNMP测试
                // 简化示例：假设public社区可用
                if ("public".equals(community)) {
                    validCommunities.pushString(community);
                }
            } catch (Exception e) {
                // 社区字符串无效
            }
        }
        
        deviceInfo.putArray("snmpCommunities", validCommunities);
        deviceInfo.putBoolean("snmpSupported", validCommunities.size() > 0);
    }
    
    /**
     * 设备指纹识别
     */
    private void performDeviceFingerprinting(String ipAddress, WritableMap deviceInfo) {
        try {
            // HTTP指纹识别
            String httpBanner = getHTTPBanner(ipAddress);
            if (httpBanner != null) {
                deviceInfo.putString("httpBanner", httpBanner);
                
                // 分析HTTP响应头识别设备类型
                String deviceType = analyzeHTTPBanner(httpBanner);
                deviceInfo.putString("detectedType", deviceType);
            }
            
            // SSH指纹识别
            String sshBanner = getSSHBanner(ipAddress);
            if (sshBanner != null) {
                deviceInfo.putString("sshBanner", sshBanner);
            }
            
        } catch (Exception e) {
            // 指纹识别失败
        }
    }
    
    /**
     * 获取MAC地址厂商信息
     */
    private String getMacVendor(String macAddress) {
        if (macAddress == null || macAddress.length() < 8) {
            return "Unknown";
        }
        
        // 提取OUI (前6位)
        String oui = macAddress.substring(0, 8).toUpperCase();
        
        // 常见厂商OUI映射
        switch (oui) {
            case "00:1A:2B": return "Cisco";
            case "00:1B:2C": return "HP";
            case "00:1C:2D": return "Dell";
            case "00:1D:2E": return "Huawei";
            case "00:1E:2F": return "Apple";
            case "00:1F:30": return "Samsung";
            case "00:20:31": return "Xiaomi";
            default: return "Unknown";
        }
    }
    
    /**
     * 推断设备类型
     */
    private String inferDeviceType(String hostname, String mac, String vendor) {
        if (hostname != null) {
            String lowerHostname = hostname.toLowerCase();
            
            if (lowerHostname.contains("router") || lowerHostname.contains("gateway")) {
                return "router";
            } else if (lowerHostname.contains("switch")) {
                return "switch";
            } else if (lowerHostname.contains("printer")) {
                return "printer";
            } else if (lowerHostname.contains("camera") || lowerHostname.contains("ipc")) {
                return "camera";
            } else if (lowerHostname.contains("nas") || lowerHostname.contains("storage")) {
                return "storage";
            } else if (lowerHostname.contains("phone") || lowerHostname.contains("mobile")) {
                return "mobile";
            } else if (lowerHostname.contains("pc") || lowerHostname.contains("desktop")) {
                return "computer";
            }
        }
        
        // 基于厂商推断
        if (vendor != null) {
            switch (vendor.toLowerCase()) {
                case "cisco":
                case "huawei":
                    return "router";
                case "hp":
                    return "printer";
                case "apple":
                case "samsung":
                case "xiaomi":
                    return "mobile";
                default:
                    return "unknown";
            }
        }
        
        return "unknown";
    }
    
    /**
     * 识别端口服务
     */
    private String identifyService(int port) {
        switch (port) {
            case 21: return "FTP";
            case 22: return "SSH";
            case 23: return "Telnet";
            case 25: return "SMTP";
            case 53: return "DNS";
            case 80: return "HTTP";
            case 110: return "POP3";
            case 143: return "IMAP";
            case 161: return "SNMP";
            case 443: return "HTTPS";
            case 993: return "IMAPS";
            case 995: return "POP3S";
            case 8080: return "HTTP-Alt";
            case 9100: return "Printer";
            default: return null;
        }
    }
    
    /**
     * 获取HTTP横幅信息
     */
    private String getHTTPBanner(String ipAddress) {
        try {
            // 实现HTTP请求获取Server头信息
            // 这里需要使用OkHttp或其他HTTP客户端
            return null; // 简化实现
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 获取SSH横幅信息
     */
    private String getSSHBanner(String ipAddress) {
        try {
            // 实现SSH连接获取横幅信息
            return null; // 简化实现
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 分析HTTP横幅识别设备类型
     */
    private String analyzeHTTPBanner(String banner) {
        if (banner == null) return "unknown";
        
        String lowerBanner = banner.toLowerCase();
        
        if (lowerBanner.contains("cisco")) {
            return "cisco-device";
        } else if (lowerBanner.contains("hp") || lowerBanner.contains("hewlett")) {
            return "hp-device";
        } else if (lowerBanner.contains("apache") || lowerBanner.contains("nginx")) {
            return "server";
        } else if (lowerBanner.contains("printer")) {
            return "printer";
        } else if (lowerBanner.contains("camera")) {
            return "camera";
        }
        
        return "unknown";
    }
}
