// 增强的设备扫描组件
import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { Wifi, Search, CheckCircle, AlertCircle, Clock, Zap } from 'lucide-react-native';
import EnhancedNetworkScanner, { EnhancedDeviceInfo, ScanProgress } from '../services/EnhancedNetworkScanner';

interface Props {
  onDeviceSelect?: (device: EnhancedDeviceInfo) => void;
}

const EnhancedDeviceScanner: React.FC<Props> = ({ onDeviceSelect }) => {
  const [devices, setDevices] = useState<EnhancedDeviceInfo[]>([]);
  const [scanning, setScanning] = useState(false);
  const [progress, setProgress] = useState<ScanProgress | null>(null);
  const [scanStats, setScanStats] = useState({
    totalDevices: 0,
    snmpDevices: 0,
    routerDevices: 0,
    printerDevices: 0
  });

  useEffect(() => {
    // 组件加载时自动扫描
    handleStartScan();
  }, []);

  const handleStartScan = async () => {
    if (scanning) return;

    setScanning(true);
    setDevices([]);
    setProgress(null);
    
    try {
      const discoveredDevices = await EnhancedNetworkScanner.scanNetwork({
        enablePortScan: true,
        enableSNMPCheck: true,
        enableFingerprinting: true,
        onProgress: (progress) => {
          setProgress(progress);
        },
        onDeviceFound: (device) => {
          setDevices(prev => {
            const updated = [...prev, device];
            updateScanStats(updated);
            return updated;
          });
        }
      });

      setDevices(discoveredDevices);
      updateScanStats(discoveredDevices);
      
    } catch (error) {
      console.error('扫描失败:', error);
      Alert.alert('扫描失败', error.message || '网络扫描过程中发生错误');
    } finally {
      setScanning(false);
      setProgress(null);
    }
  };

  const updateScanStats = (deviceList: EnhancedDeviceInfo[]) => {
    const stats = {
      totalDevices: deviceList.length,
      snmpDevices: deviceList.filter(d => d.snmpSupported).length,
      routerDevices: deviceList.filter(d => d.type === 'router').length,
      printerDevices: deviceList.filter(d => d.type === 'printer').length
    };
    setScanStats(stats);
  };

  const getDeviceIcon = (device: EnhancedDeviceInfo) => {
    const iconProps = { size: 24, color: getDeviceColor(device) };
    
    switch (device.type) {
      case 'router':
        return <Wifi {...iconProps} />;
      case 'printer':
        return <Search {...iconProps} />;
      case 'server':
        return <Zap {...iconProps} />;
      default:
        return <CheckCircle {...iconProps} />;
    }
  };

  const getDeviceColor = (device: EnhancedDeviceInfo) => {
    if (!device.isReachable) return '#ff4d4f';
    if (device.snmpSupported) return '#52c41a';
    if (device.confidence > 70) return '#1890ff';
    return '#faad14';
  };

  const getConfidenceText = (confidence: number) => {
    if (confidence >= 80) return '高';
    if (confidence >= 60) return '中';
    return '低';
  };

  const formatResponseTime = (time?: number) => {
    if (!time) return 'N/A';
    return `${time.toFixed(0)}ms`;
  };

  const renderScanProgress = () => {
    if (!progress) return null;

    return (
      <View style={styles.progressContainer}>
        <View style={styles.progressHeader}>
          <Text style={styles.progressTitle}>
            {progress.phase === 'discovery' && '🔍 发现设备'}
            {progress.phase === 'port_scan' && '🔌 端口扫描'}
            {progress.phase === 'snmp_check' && '📡 SNMP检测'}
            {progress.phase === 'fingerprint' && '🔍 设备识别'}
            {progress.phase === 'complete' && '✅ 扫描完成'}
          </Text>
          <Text style={styles.progressPercent}>{progress.progress.toFixed(0)}%</Text>
        </View>
        
        <View style={styles.progressBar}>
          <View 
            style={[styles.progressFill, { width: `${progress.progress}%` }]} 
          />
        </View>
        
        <Text style={styles.progressMessage}>{progress.message}</Text>
        {progress.currentIP && (
          <Text style={styles.progressIP}>当前: {progress.currentIP}</Text>
        )}
      </View>
    );
  };

  const renderScanStats = () => (
    <View style={styles.statsContainer}>
      <View style={styles.statItem}>
        <Text style={styles.statNumber}>{scanStats.totalDevices}</Text>
        <Text style={styles.statLabel}>总设备</Text>
      </View>
      <View style={styles.statItem}>
        <Text style={styles.statNumber}>{scanStats.snmpDevices}</Text>
        <Text style={styles.statLabel}>SNMP设备</Text>
      </View>
      <View style={styles.statItem}>
        <Text style={styles.statNumber}>{scanStats.routerDevices}</Text>
        <Text style={styles.statLabel}>路由器</Text>
      </View>
      <View style={styles.statItem}>
        <Text style={styles.statNumber}>{scanStats.printerDevices}</Text>
        <Text style={styles.statLabel}>打印机</Text>
      </View>
    </View>
  );

  const renderDeviceCard = (device: EnhancedDeviceInfo) => (
    <TouchableOpacity
      key={device.id}
      style={[styles.deviceCard, { borderLeftColor: getDeviceColor(device) }]}
      onPress={() => onDeviceSelect?.(device)}
    >
      <View style={styles.deviceHeader}>
        <View style={styles.deviceIcon}>
          {getDeviceIcon(device)}
        </View>
        <View style={styles.deviceInfo}>
          <Text style={styles.deviceName}>{device.name}</Text>
          <Text style={styles.deviceIP}>{device.ipAddress}</Text>
        </View>
        <View style={styles.deviceStatus}>
          {device.isReachable ? (
            <CheckCircle size={16} color="#52c41a" />
          ) : (
            <AlertCircle size={16} color="#ff4d4f" />
          )}
        </View>
      </View>

      <View style={styles.deviceDetails}>
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>类型:</Text>
          <Text style={styles.detailValue}>{device.type}</Text>
        </View>
        
        {device.vendor && (
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>厂商:</Text>
            <Text style={styles.detailValue}>{device.vendor}</Text>
          </View>
        )}
        
        {device.macAddress && (
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>MAC:</Text>
            <Text style={styles.detailValue}>{device.macAddress}</Text>
          </View>
        )}
        
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>响应时间:</Text>
          <Text style={styles.detailValue}>{formatResponseTime(device.responseTime)}</Text>
        </View>
        
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>置信度:</Text>
          <Text style={[styles.detailValue, { color: getDeviceColor(device) }]}>
            {getConfidenceText(device.confidence)} ({device.confidence}%)
          </Text>
        </View>
      </View>

      {device.services.length > 0 && (
        <View style={styles.servicesContainer}>
          <Text style={styles.servicesTitle}>服务:</Text>
          <View style={styles.servicesList}>
            {device.services.slice(0, 4).map((service, index) => (
              <View key={index} style={styles.serviceTag}>
                <Text style={styles.serviceText}>
                  {service.service}:{service.port}
                </Text>
              </View>
            ))}
            {device.services.length > 4 && (
              <Text style={styles.moreServices}>+{device.services.length - 4}</Text>
            )}
          </View>
        </View>
      )}

      {device.snmpSupported && (
        <View style={styles.snmpIndicator}>
          <Text style={styles.snmpText}>📡 支持SNMP</Text>
        </View>
      )}
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={[styles.scanButton, scanning && styles.scanButtonDisabled]}
          onPress={handleStartScan}
          disabled={scanning}
        >
          <Search size={20} color="#fff" />
          <Text style={styles.scanButtonText}>
            {scanning ? '扫描中...' : '重新扫描'}
          </Text>
        </TouchableOpacity>
      </View>

      {renderScanStats()}
      
      {scanning && renderScanProgress()}

      <ScrollView style={styles.devicesList} showsVerticalScrollIndicator={false}>
        {devices.map(renderDeviceCard)}
        
        {!scanning && devices.length === 0 && (
          <View style={styles.emptyState}>
            <AlertCircle size={48} color="#d9d9d9" />
            <Text style={styles.emptyText}>未发现设备</Text>
            <Text style={styles.emptySubtext}>请检查网络连接或重新扫描</Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e8e8e8',
  },
  scanButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#1890ff',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    gap: 8,
  },
  scanButtonDisabled: {
    backgroundColor: '#d9d9d9',
  },
  scanButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  statsContainer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e8e8e8',
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1890ff',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  progressContainer: {
    backgroundColor: '#fff',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e8e8e8',
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  progressPercent: {
    fontSize: 14,
    color: '#1890ff',
    fontWeight: '600',
  },
  progressBar: {
    height: 4,
    backgroundColor: '#e8e8e8',
    borderRadius: 2,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#1890ff',
    borderRadius: 2,
  },
  progressMessage: {
    fontSize: 14,
    color: '#666',
  },
  progressIP: {
    fontSize: 12,
    color: '#999',
    marginTop: 4,
  },
  devicesList: {
    flex: 1,
    padding: 16,
  },
  deviceCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  deviceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  deviceIcon: {
    marginRight: 12,
  },
  deviceInfo: {
    flex: 1,
  },
  deviceName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  deviceIP: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  deviceStatus: {
    marginLeft: 8,
  },
  deviceDetails: {
    marginBottom: 12,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  detailLabel: {
    fontSize: 14,
    color: '#666',
  },
  detailValue: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  servicesContainer: {
    marginBottom: 8,
  },
  servicesTitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  servicesList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
  },
  serviceTag: {
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  serviceText: {
    fontSize: 12,
    color: '#666',
  },
  moreServices: {
    fontSize: 12,
    color: '#999',
    alignSelf: 'center',
  },
  snmpIndicator: {
    backgroundColor: '#f6ffed',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    alignSelf: 'flex-start',
  },
  snmpText: {
    fontSize: 12,
    color: '#52c41a',
    fontWeight: '500',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 48,
  },
  emptyText: {
    fontSize: 18,
    color: '#999',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#ccc',
    marginTop: 8,
  },
});

export default EnhancedDeviceScanner;
