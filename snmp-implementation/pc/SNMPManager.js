// PC客户端 SNMP管理器 (Node.js)
const snmp = require('net-snmp');
const EventEmitter = require('events');

/**
 * 专业级SNMP管理器
 */
class SNMPManager extends EventEmitter {
  constructor() {
    super();
    this.sessions = new Map(); // 存储SNMP会话
    this.monitoringTasks = new Map(); // 存储监控任务
    this.mibDatabase = new Map(); // MIB数据库
    
    this.initializeMIBDatabase();
  }

  /**
   * 初始化MIB数据库
   */
  initializeMIBDatabase() {
    // 系统MIB
    this.mibDatabase.set('*******.*******.0', { name: 'sysDescr', type: 'string', description: '系统描述' });
    this.mibDatabase.set('*******.*******.0', { name: 'sysObjectID', type: 'oid', description: '系统对象ID' });
    this.mibDatabase.set('*******.*******.0', { name: 'sysUpTime', type: 'timeticks', description: '系统运行时间' });
    this.mibDatabase.set('*******.*******.0', { name: 'sysContact', type: 'string', description: '系统联系人' });
    this.mibDatabase.set('*******.*******.0', { name: 'sysName', type: 'string', description: '系统名称' });
    this.mibDatabase.set('*******.*******.0', { name: 'sysLocation', type: 'string', description: '系统位置' });
    
    // 接口MIB
    this.mibDatabase.set('*******.*******.0', { name: 'ifNumber', type: 'integer', description: '接口数量' });
    this.mibDatabase.set('*******.*******.1.1', { name: 'ifIndex', type: 'integer', description: '接口索引' });
    this.mibDatabase.set('*******.*******.1.2', { name: 'ifDescr', type: 'string', description: '接口描述' });
    this.mibDatabase.set('*******.*******.1.3', { name: 'ifType', type: 'integer', description: '接口类型' });
    this.mibDatabase.set('*******.*******.1.5', { name: 'ifSpeed', type: 'gauge', description: '接口速度' });
    this.mibDatabase.set('*******.*******.1.8', { name: 'ifOperStatus', type: 'integer', description: '接口操作状态' });
    
    // 主机资源MIB
    this.mibDatabase.set('*******.********.1.0', { name: 'hrSystemUptime', type: 'timeticks', description: '主机运行时间' });
    this.mibDatabase.set('*******.********.2.0', { name: 'hrMemorySize', type: 'integer', description: '内存大小' });
    this.mibDatabase.set('*******.********.3.1.2', { name: 'hrProcessorLoad', type: 'integer', description: 'CPU负载' });
  }

  /**
   * 创建SNMP会话
   */
  createSession(target, options = {}) {
    const sessionOptions = {
      port: options.port || 161,
      retries: options.retries || 1,
      timeout: options.timeout || 5000,
      transport: options.transport || 'udp4',
      trapPort: options.trapPort || 162,
      version: options.version || snmp.Version2c,
      community: options.community || 'public'
    };

    const session = snmp.createSession(target, sessionOptions.community, sessionOptions);
    
    session.on('error', (error) => {
      console.error(`SNMP会话错误 ${target}:`, error);
      this.emit('sessionError', { target, error });
    });

    this.sessions.set(target, session);
    return session;
  }

  /**
   * 高级网络扫描
   */
  async advancedNetworkScan(options = {}) {
    const {
      subnets = ['***********/24'],
      communities = ['public', 'private'],
      ports = [161],
      timeout = 3000,
      maxConcurrent = 50
    } = options;

    console.log('🔍 开始高级网络扫描...');
    const devices = [];
    const scanPromises = [];

    for (const subnet of subnets) {
      const ips = this.generateIPRange(subnet);
      
      for (const ip of ips) {
        for (const community of communities) {
          for (const port of ports) {
            // 控制并发数量
            if (scanPromises.length >= maxConcurrent) {
              const results = await Promise.allSettled(scanPromises);
              this.processScanResults(results, devices);
              scanPromises.length = 0;
            }

            scanPromises.push(this.scanDevice(ip, { community, port, timeout }));
          }
        }
      }
    }

    // 处理剩余扫描
    if (scanPromises.length > 0) {
      const results = await Promise.allSettled(scanPromises);
      this.processScanResults(results, devices);
    }

    console.log(`✅ 扫描完成，发现 ${devices.length} 个设备`);
    return devices;
  }

  /**
   * 扫描单个设备
   */
  async scanDevice(ipAddress, options = {}) {
    const { community = 'public', port = 161, timeout = 3000 } = options;
    
    try {
      const session = this.createSession(ipAddress, { community, port, timeout });
      
      // 获取基本系统信息
      const systemInfo = await this.getSystemInfo(session);
      
      if (systemInfo.sysDescr) {
        const deviceInfo = {
          ipAddress,
          port,
          community,
          ...systemInfo,
          deviceType: this.identifyDeviceType(systemInfo),
          vendor: this.identifyVendor(systemInfo),
          capabilities: await this.detectCapabilities(session),
          discoveredAt: new Date().toISOString()
        };

        session.close();
        return deviceInfo;
      }
    } catch (error) {
      // 设备不支持SNMP或网络不可达
      return null;
    }
    
    return null;
  }

  /**
   * 获取系统信息
   */
  async getSystemInfo(session) {
    const systemOIDs = [
      '*******.*******.0', // sysDescr
      '*******.*******.0', // sysObjectID
      '*******.*******.0', // sysUpTime
      '*******.*******.0', // sysContact
      '*******.*******.0', // sysName
      '*******.*******.0'  // sysLocation
    ];

    return new Promise((resolve, reject) => {
      session.get(systemOIDs, (error, varbinds) => {
        if (error) {
          reject(error);
          return;
        }

        const systemInfo = {};
        varbinds.forEach((vb, index) => {
          if (snmp.isVarbindError(vb)) {
            console.warn(`获取 ${systemOIDs[index]} 失败:`, snmp.varbindError(vb));
          } else {
            const mibInfo = this.mibDatabase.get(systemOIDs[index]);
            const key = mibInfo ? mibInfo.name : `oid_${index}`;
            systemInfo[key] = this.formatValue(vb.value, mibInfo?.type);
          }
        });

        resolve(systemInfo);
      });
    });
  }

  /**
   * 获取接口表信息
   */
  async getInterfaceTable(session) {
    return new Promise((resolve, reject) => {
      const interfaces = [];
      
      // 遍历接口表
      session.tableColumns(['*******.*******.1.1', '*******.*******.1.2', '*******.*******.1.3', 
                           '*******.*******.1.5', '*******.*******.1.8', '*******.*******.1.10', 
                           '*******.*******.1.16'], (error, table) => {
        if (error) {
          reject(error);
          return;
        }

        for (const index in table) {
          const row = table[index];
          interfaces.push({
            index: row['*******.*******.1.1'],
            description: row['*******.*******.1.2']?.toString() || '',
            type: this.getInterfaceTypeName(row['*******.*******.1.3']),
            speed: this.formatSpeed(row['*******.*******.1.5']),
            operStatus: this.getOperStatusName(row['*******.*******.1.8']),
            inOctets: row['*******.*******.1.10'] || 0,
            outOctets: row['*******.*******.1.16'] || 0
          });
        }

        resolve(interfaces);
      });
    });
  }

  /**
   * 获取性能监控数据
   */
  async getPerformanceData(session) {
    const performanceOIDs = [
      '*******.********.*******', // CPU负载
      '*******.********.2.0',     // 内存大小
      '*******.4.1.2021.********', // 1分钟负载平均值 (UCD-SNMP)
      '*******.4.1.2021.********', // 5分钟负载平均值
      '*******.4.1.2021.********'  // 15分钟负载平均值
    ];

    return new Promise((resolve, reject) => {
      session.get(performanceOIDs, (error, varbinds) => {
        if (error) {
          reject(error);
          return;
        }

        const performance = {
          timestamp: new Date().toISOString()
        };

        varbinds.forEach((vb, index) => {
          if (!snmp.isVarbindError(vb)) {
            switch (index) {
              case 0:
                performance.cpuLoad = parseInt(vb.value) || 0;
                break;
              case 1:
                performance.memorySize = this.formatBytes(vb.value);
                break;
              case 2:
                performance.loadAvg1min = parseFloat(vb.value) || 0;
                break;
              case 3:
                performance.loadAvg5min = parseFloat(vb.value) || 0;
                break;
              case 4:
                performance.loadAvg15min = parseFloat(vb.value) || 0;
                break;
            }
          }
        });

        resolve(performance);
      });
    });
  }

  /**
   * 开始监控设备
   */
  startMonitoring(ipAddress, options = {}) {
    const {
      interval = 30000,
      community = 'public',
      metrics = ['system', 'interfaces', 'performance']
    } = options;

    if (this.monitoringTasks.has(ipAddress)) {
      console.warn(`设备 ${ipAddress} 已在监控中`);
      return;
    }

    const session = this.createSession(ipAddress, { community });
    
    const monitoringTask = setInterval(async () => {
      try {
        const data = {
          ipAddress,
          timestamp: new Date().toISOString()
        };

        if (metrics.includes('system')) {
          data.system = await this.getSystemInfo(session);
        }
        
        if (metrics.includes('interfaces')) {
          data.interfaces = await this.getInterfaceTable(session);
        }
        
        if (metrics.includes('performance')) {
          data.performance = await this.getPerformanceData(session);
        }

        this.emit('monitoringData', data);
        
        // 检查告警条件
        this.checkAlerts(data);

      } catch (error) {
        console.error(`监控设备 ${ipAddress} 失败:`, error);
        this.emit('monitoringError', { ipAddress, error });
      }
    }, interval);

    this.monitoringTasks.set(ipAddress, { task: monitoringTask, session });
    console.log(`✅ 开始监控设备: ${ipAddress}`);
  }

  /**
   * 停止监控设备
   */
  stopMonitoring(ipAddress) {
    const monitoring = this.monitoringTasks.get(ipAddress);
    if (monitoring) {
      clearInterval(monitoring.task);
      monitoring.session.close();
      this.monitoringTasks.delete(ipAddress);
      console.log(`⏹️ 停止监控设备: ${ipAddress}`);
    }
  }

  /**
   * 批量配置设备
   */
  async batchConfiguration(devices, configData) {
    const results = [];
    
    for (const device of devices) {
      try {
        const result = await this.configureDevice(device.ipAddress, configData, {
          community: device.community || 'private'
        });
        results.push({ device: device.ipAddress, success: true, result });
      } catch (error) {
        results.push({ device: device.ipAddress, success: false, error: error.message });
      }
    }
    
    return results;
  }

  // 辅助方法
  generateIPRange(subnet) {
    // 简化的IP范围生成，实际应该支持CIDR
    const [network, mask] = subnet.split('/');
    const [a, b, c, d] = network.split('.').map(Number);
    const ips = [];
    
    if (mask === '24') {
      for (let i = 1; i < 255; i++) {
        ips.push(`${a}.${b}.${c}.${i}`);
      }
    }
    
    return ips;
  }

  processScanResults(results, devices) {
    results.forEach(result => {
      if (result.status === 'fulfilled' && result.value) {
        devices.push(result.value);
      }
    });
  }

  identifyDeviceType(systemInfo) {
    const desc = (systemInfo.sysDescr || '').toLowerCase();
    
    if (desc.includes('router')) return 'router';
    if (desc.includes('switch')) return 'switch';
    if (desc.includes('firewall')) return 'firewall';
    if (desc.includes('access point')) return 'accesspoint';
    if (desc.includes('server')) return 'server';
    if (desc.includes('printer')) return 'printer';
    
    return 'unknown';
  }

  identifyVendor(systemInfo) {
    const desc = (systemInfo.sysDescr || '').toLowerCase();
    const vendors = ['cisco', 'huawei', 'hp', 'dell', 'juniper', 'aruba'];
    
    for (const vendor of vendors) {
      if (desc.includes(vendor)) {
        return vendor.charAt(0).toUpperCase() + vendor.slice(1);
      }
    }
    
    return 'Unknown';
  }

  async detectCapabilities(session) {
    // 检测设备支持的MIB和功能
    const capabilities = [];
    
    // 检测是否支持接口MIB
    try {
      await this.getInterfaceTable(session);
      capabilities.push('interfaces');
    } catch (e) {
      // 不支持接口MIB
    }
    
    return capabilities;
  }

  formatValue(value, type) {
    switch (type) {
      case 'timeticks':
        return this.formatUptime(value);
      case 'gauge':
      case 'integer':
        return parseInt(value) || 0;
      default:
        return value?.toString() || '';
    }
  }

  formatUptime(ticks) {
    const seconds = Math.floor(ticks / 100);
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    return `${days}天 ${hours}小时 ${minutes}分钟`;
  }

  formatSpeed(speed) {
    const speedValue = parseInt(speed) || 0;
    if (speedValue >= 1000000000) {
      return `${speedValue / 1000000000} Gbps`;
    } else if (speedValue >= 1000000) {
      return `${speedValue / 1000000} Mbps`;
    } else if (speedValue >= 1000) {
      return `${speedValue / 1000} Kbps`;
    }
    return `${speedValue} bps`;
  }

  formatBytes(bytes) {
    const size = parseInt(bytes) || 0;
    if (size >= 1073741824) {
      return `${(size / 1073741824).toFixed(2)} GB`;
    } else if (size >= 1048576) {
      return `${(size / 1048576).toFixed(2)} MB`;
    } else if (size >= 1024) {
      return `${(size / 1024).toFixed(2)} KB`;
    }
    return `${size} B`;
  }

  getInterfaceTypeName(type) {
    const types = {
      1: 'Other', 6: 'Ethernet', 24: 'Loopback', 131: 'Tunnel'
    };
    return types[type] || `Type ${type}`;
  }

  getOperStatusName(status) {
    const statuses = {
      1: 'Up', 2: 'Down', 3: 'Testing', 4: 'Unknown', 5: 'Dormant', 6: 'NotPresent', 7: 'LowerLayerDown'
    };
    return statuses[status] || `Status ${status}`;
  }

  checkAlerts(data) {
    const alerts = [];
    
    if (data.performance) {
      if (data.performance.cpuLoad > 90) {
        alerts.push({
          type: 'critical',
          metric: 'cpu',
          value: data.performance.cpuLoad,
          threshold: 90,
          message: `CPU使用率过高: ${data.performance.cpuLoad}%`
        });
      }
    }
    
    if (alerts.length > 0) {
      this.emit('alerts', { device: data.ipAddress, alerts });
    }
  }
}

module.exports = SNMPManager;
