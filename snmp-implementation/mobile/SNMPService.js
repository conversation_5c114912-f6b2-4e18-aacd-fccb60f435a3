// React Native SNMP服务封装
import { NativeModules, Platform } from 'react-native';

const { SNMPModule } = NativeModules;

/**
 * SNMP服务类
 */
class SNMPService {
  constructor() {
    this.defaultCommunity = 'public';
    this.timeout = 5000;
  }

  /**
   * 检查SNMP模块是否可用
   */
  isAvailable() {
    return SNMPModule !== null && SNMPModule !== undefined;
  }

  /**
   * 扫描网络中的SNMP设备
   */
  async scanNetwork(subnet = '192.168.1', startIP = 1, endIP = 254) {
    const devices = [];
    const promises = [];

    console.log(`🔍 开始扫描网络: ${subnet}.${startIP}-${endIP}`);

    // 并发扫描多个IP
    for (let i = startIP; i <= endIP; i++) {
      const ip = `${subnet}.${i}`;
      
      // 限制并发数量，避免网络拥塞
      if (promises.length >= 20) {
        const results = await Promise.allSettled(promises);
        this.processResults(results, devices);
        promises.length = 0;
      }

      promises.push(this.checkDevice(ip));
    }

    // 处理剩余的请求
    if (promises.length > 0) {
      const results = await Promise.allSettled(promises);
      this.processResults(results, devices);
    }

    console.log(`✅ 扫描完成，发现 ${devices.length} 个SNMP设备`);
    return devices;
  }

  /**
   * 检查单个设备是否支持SNMP
   */
  async checkDevice(ipAddress, community = this.defaultCommunity) {
    try {
      // 尝试获取系统描述来验证SNMP可用性
      const deviceInfo = await this.getBasicDeviceInfo(ipAddress, community);
      
      if (deviceInfo && deviceInfo.description) {
        return {
          ipAddress,
          isSnmpEnabled: true,
          deviceInfo,
          lastChecked: new Date().toISOString()
        };
      }
    } catch (error) {
      // SNMP不可用或设备不存在
      return null;
    }
    
    return null;
  }

  /**
   * 获取设备基本信息
   */
  async getBasicDeviceInfo(ipAddress, community = this.defaultCommunity) {
    try {
      console.log(`📡 获取设备信息: ${ipAddress}`);
      
      const deviceInfo = await SNMPModule.getDeviceInfo(ipAddress, community);
      
      // 解析设备类型
      const deviceType = this.parseDeviceType(deviceInfo.description);
      
      return {
        ...deviceInfo,
        deviceType,
        vendor: this.parseVendor(deviceInfo.description),
        model: this.parseModel(deviceInfo.description)
      };
    } catch (error) {
      console.error(`❌ 获取设备信息失败 ${ipAddress}:`, error.message);
      throw error;
    }
  }

  /**
   * 获取设备接口信息
   */
  async getInterfaceInfo(ipAddress, community = this.defaultCommunity) {
    try {
      console.log(`🔌 获取接口信息: ${ipAddress}`);
      
      const interfaces = await SNMPModule.getInterfaceInfo(ipAddress, community);
      
      // 过滤和增强接口信息
      return interfaces
        .filter(iface => iface.description && iface.description !== '')
        .map(iface => ({
          ...iface,
          utilization: this.calculateUtilization(iface),
          statusColor: this.getStatusColor(iface.status)
        }));
    } catch (error) {
      console.error(`❌ 获取接口信息失败 ${ipAddress}:`, error.message);
      throw error;
    }
  }

  /**
   * 获取设备性能信息
   */
  async getPerformanceInfo(ipAddress, community = this.defaultCommunity) {
    try {
      console.log(`📊 获取性能信息: ${ipAddress}`);
      
      const performance = await SNMPModule.getPerformanceInfo(ipAddress, community);
      
      return {
        ...performance,
        timestamp: new Date().toISOString(),
        healthScore: this.calculateHealthScore(performance)
      };
    } catch (error) {
      console.error(`❌ 获取性能信息失败 ${ipAddress}:`, error.message);
      throw error;
    }
  }

  /**
   * 获取完整设备信息
   */
  async getCompleteDeviceInfo(ipAddress, community = this.defaultCommunity) {
    try {
      console.log(`🔍 获取完整设备信息: ${ipAddress}`);
      
      const [basicInfo, interfaces, performance] = await Promise.allSettled([
        this.getBasicDeviceInfo(ipAddress, community),
        this.getInterfaceInfo(ipAddress, community),
        this.getPerformanceInfo(ipAddress, community)
      ]);

      return {
        ipAddress,
        basicInfo: basicInfo.status === 'fulfilled' ? basicInfo.value : null,
        interfaces: interfaces.status === 'fulfilled' ? interfaces.value : [],
        performance: performance.status === 'fulfilled' ? performance.value : null,
        lastUpdated: new Date().toISOString(),
        errors: [
          basicInfo.status === 'rejected' ? basicInfo.reason : null,
          interfaces.status === 'rejected' ? interfaces.reason : null,
          performance.status === 'rejected' ? performance.reason : null
        ].filter(Boolean)
      };
    } catch (error) {
      console.error(`❌ 获取完整设备信息失败 ${ipAddress}:`, error.message);
      throw error;
    }
  }

  /**
   * 监控设备状态变化
   */
  async monitorDevice(ipAddress, community = this.defaultCommunity, interval = 30000) {
    const monitorId = setInterval(async () => {
      try {
        const deviceInfo = await this.getCompleteDeviceInfo(ipAddress, community);
        
        // 触发状态更新事件
        this.onDeviceStatusUpdate?.(deviceInfo);
        
        // 检查告警条件
        this.checkAlerts(deviceInfo);
        
      } catch (error) {
        console.error(`监控设备失败 ${ipAddress}:`, error.message);
        this.onDeviceError?.(ipAddress, error);
      }
    }, interval);

    return monitorId;
  }

  /**
   * 停止监控
   */
  stopMonitoring(monitorId) {
    if (monitorId) {
      clearInterval(monitorId);
    }
  }

  // 辅助方法
  processResults(results, devices) {
    results.forEach(result => {
      if (result.status === 'fulfilled' && result.value) {
        devices.push(result.value);
      }
    });
  }

  parseDeviceType(description) {
    if (!description) return 'unknown';
    
    const desc = description.toLowerCase();
    
    if (desc.includes('router')) return 'router';
    if (desc.includes('switch')) return 'switch';
    if (desc.includes('firewall')) return 'firewall';
    if (desc.includes('access point') || desc.includes('ap')) return 'accesspoint';
    if (desc.includes('server')) return 'server';
    if (desc.includes('printer')) return 'printer';
    if (desc.includes('camera')) return 'camera';
    if (desc.includes('nas')) return 'storage';
    
    return 'other';
  }

  parseVendor(description) {
    if (!description) return 'Unknown';
    
    const vendors = ['Cisco', 'Huawei', 'HP', 'Dell', 'Juniper', 'Aruba', 'Ubiquiti'];
    
    for (const vendor of vendors) {
      if (description.toLowerCase().includes(vendor.toLowerCase())) {
        return vendor;
      }
    }
    
    return 'Unknown';
  }

  parseModel(description) {
    if (!description) return 'Unknown';
    
    // 尝试从描述中提取型号信息
    const modelMatch = description.match(/([A-Z0-9\-]+\d+[A-Z0-9\-]*)/);
    return modelMatch ? modelMatch[1] : 'Unknown';
  }

  calculateUtilization(interfaceInfo) {
    // 简单的利用率计算示例
    const inOctets = parseInt(interfaceInfo.inOctets) || 0;
    const outOctets = parseInt(interfaceInfo.outOctets) || 0;
    const speed = parseInt(interfaceInfo.speed) || 1;
    
    // 这里需要更复杂的计算逻辑
    return Math.min(((inOctets + outOctets) / speed) * 100, 100);
  }

  getStatusColor(status) {
    switch (status) {
      case 'Up': return '#52c41a';
      case 'Down': return '#ff4d4f';
      case 'Testing': return '#faad14';
      default: return '#d9d9d9';
    }
  }

  calculateHealthScore(performance) {
    // 简单的健康评分算法
    let score = 100;
    
    const cpuLoad = parseInt(performance.cpuLoad) || 0;
    if (cpuLoad > 80) score -= 30;
    else if (cpuLoad > 60) score -= 15;
    
    // 可以添加更多评分逻辑
    
    return Math.max(score, 0);
  }

  checkAlerts(deviceInfo) {
    const alerts = [];
    
    if (deviceInfo.performance) {
      const cpuLoad = parseInt(deviceInfo.performance.cpuLoad) || 0;
      if (cpuLoad > 90) {
        alerts.push({
          type: 'critical',
          message: `CPU使用率过高: ${cpuLoad}%`,
          device: deviceInfo.ipAddress
        });
      }
    }
    
    if (alerts.length > 0) {
      this.onAlert?.(alerts);
    }
  }

  // 事件回调
  onDeviceStatusUpdate = null;
  onDeviceError = null;
  onAlert = null;
}

export default new SNMPService();
