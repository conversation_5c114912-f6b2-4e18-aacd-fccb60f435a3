// MIB数据库管理系统
class MIBDatabase {
  constructor() {
    this.mibs = new Map();
    this.vendors = new Map();
    this.deviceProfiles = new Map();
    
    this.initializeStandardMIBs();
    this.initializeVendorMIBs();
  }

  /**
   * 初始化标准MIB库
   */
  initializeStandardMIBs() {
    // RFC 1213 - MIB-II
    this.addMIBGroup('system', '*******.2.1.1', {
      'sysDescr': { oid: '*******.*******.0', type: 'DisplayString', access: 'read-only', description: '系统描述信息' },
      'sysObjectID': { oid: '*******.*******.0', type: 'OBJECT IDENTIFIER', access: 'read-only', description: '系统对象标识符' },
      'sysUpTime': { oid: '*******.*******.0', type: 'TimeTicks', access: 'read-only', description: '系统启动后的时间' },
      'sysContact': { oid: '*******.*******.0', type: 'DisplayString', access: 'read-write', description: '系统联系人' },
      'sysName': { oid: '*******.*******.0', type: 'DisplayString', access: 'read-write', description: '系统名称' },
      'sysLocation': { oid: '*******.*******.0', type: 'DisplayString', access: 'read-write', description: '系统位置' },
      'sysServices': { oid: '*******.*******.0', type: 'INTEGER', access: 'read-only', description: '系统提供的服务' }
    });

    // 接口MIB
    this.addMIBGroup('interfaces', '*******.2.1.2', {
      'ifNumber': { oid: '*******.*******.0', type: 'INTEGER', access: 'read-only', description: '接口数量' },
      'ifTable': { oid: '*******.*******', type: 'SEQUENCE OF IfEntry', access: 'not-accessible', description: '接口表' },
      'ifIndex': { oid: '*******.*******.1.1', type: 'INTEGER', access: 'read-only', description: '接口索引' },
      'ifDescr': { oid: '*******.*******.1.2', type: 'DisplayString', access: 'read-only', description: '接口描述' },
      'ifType': { oid: '*******.*******.1.3', type: 'IANAifType', access: 'read-only', description: '接口类型' },
      'ifMtu': { oid: '*******.*******.1.4', type: 'INTEGER', access: 'read-only', description: '最大传输单元' },
      'ifSpeed': { oid: '*******.*******.1.5', type: 'Gauge32', access: 'read-only', description: '接口速度' },
      'ifPhysAddress': { oid: '*******.*******.1.6', type: 'PhysAddress', access: 'read-only', description: '物理地址' },
      'ifAdminStatus': { oid: '*******.*******.1.7', type: 'INTEGER', access: 'read-write', description: '管理状态' },
      'ifOperStatus': { oid: '*******.*******.1.8', type: 'INTEGER', access: 'read-only', description: '操作状态' },
      'ifLastChange': { oid: '*******.*******.1.9', type: 'TimeTicks', access: 'read-only', description: '最后状态变化时间' },
      'ifInOctets': { oid: '*******.*******.1.10', type: 'Counter32', access: 'read-only', description: '接收字节数' },
      'ifInUcastPkts': { oid: '*******.*******.1.11', type: 'Counter32', access: 'read-only', description: '接收单播包数' },
      'ifInErrors': { oid: '*******.*******.1.14', type: 'Counter32', access: 'read-only', description: '接收错误包数' },
      'ifOutOctets': { oid: '*******.*******.1.16', type: 'Counter32', access: 'read-only', description: '发送字节数' },
      'ifOutUcastPkts': { oid: '*******.*******.1.17', type: 'Counter32', access: 'read-only', description: '发送单播包数' },
      'ifOutErrors': { oid: '*******.*******.1.20', type: 'Counter32', access: 'read-only', description: '发送错误包数' }
    });

    // 主机资源MIB (RFC 2790)
    this.addMIBGroup('host', '*******.2.1.25', {
      'hrSystemUptime': { oid: '*******.********.1.0', type: 'TimeTicks', access: 'read-only', description: '主机运行时间' },
      'hrSystemDate': { oid: '*******.********.2.0', type: 'DateAndTime', access: 'read-write', description: '主机日期时间' },
      'hrMemorySize': { oid: '*******.********.2.0', type: 'KBytes', access: 'read-only', description: '物理内存大小' },
      'hrStorageTable': { oid: '*******.********.3', type: 'SEQUENCE OF HrStorageEntry', access: 'not-accessible', description: '存储表' },
      'hrProcessorTable': { oid: '*******.********.3', type: 'SEQUENCE OF HrProcessorEntry', access: 'not-accessible', description: '处理器表' },
      'hrProcessorLoad': { oid: '*******.********.3.1.2', type: 'INTEGER', access: 'read-only', description: 'CPU负载百分比' }
    });

    // IP MIB
    this.addMIBGroup('ip', '*******.2.1.4', {
      'ipForwarding': { oid: '*******.*******.0', type: 'INTEGER', access: 'read-write', description: 'IP转发状态' },
      'ipDefaultTTL': { oid: '*******.*******.0', type: 'INTEGER', access: 'read-write', description: '默认TTL值' },
      'ipInReceives': { oid: '*******.*******.0', type: 'Counter32', access: 'read-only', description: '接收IP数据包数' },
      'ipInDelivers': { oid: '*******.*******.0', type: 'Counter32', access: 'read-only', description: '成功投递的IP包数' },
      'ipOutRequests': { oid: '*******.*******0.0', type: 'Counter32', access: 'read-only', description: '发送IP请求数' }
    });

    // TCP MIB
    this.addMIBGroup('tcp', '*******.2.1.6', {
      'tcpRtoAlgorithm': { oid: '*******.*******.0', type: 'INTEGER', access: 'read-only', description: 'TCP重传超时算法' },
      'tcpRtoMin': { oid: '*******.*******.0', type: 'INTEGER', access: 'read-only', description: '最小重传超时时间' },
      'tcpRtoMax': { oid: '*******.*******.0', type: 'INTEGER', access: 'read-only', description: '最大重传超时时间' },
      'tcpMaxConn': { oid: '*******.*******.0', type: 'INTEGER', access: 'read-only', description: '最大TCP连接数' },
      'tcpActiveOpens': { oid: '*******.*******.0', type: 'Counter32', access: 'read-only', description: '主动打开连接数' },
      'tcpPassiveOpens': { oid: '*******.*******.0', type: 'Counter32', access: 'read-only', description: '被动打开连接数' },
      'tcpCurrEstab': { oid: '*******.*******.0', type: 'Gauge32', access: 'read-only', description: '当前建立的连接数' }
    });
  }

  /**
   * 初始化厂商特定MIB
   */
  initializeVendorMIBs() {
    // Cisco MIB
    this.addVendorMIB('cisco', '*******.4.1.9', {
      'ciscoMemoryPoolTable': { oid: '*******.*******.48.1.1', description: 'Cisco内存池表' },
      'ciscoCPUTable': { oid: '*******.*******.*********', description: 'Cisco CPU表' },
      'ciscoEnvMonTemperatureTable': { oid: '*******.*******.13.1.3', description: 'Cisco环境温度监控表' },
      'ciscoFlashTable': { oid: '*******.*******.********', description: 'Cisco Flash存储表' }
    });

    // Huawei MIB
    this.addVendorMIB('huawei', '*******.4.1.2011', {
      'hwEntityTable': { oid: '*******.4.1.2011.5.25.31.1.1.1', description: '华为实体表' },
      'hwCpuDevTable': { oid: '*******.4.1.2011.5.25.31.1.1.1.1', description: '华为CPU设备表' },
      'hwMemoryDevTable': { oid: '*******.4.1.2011.5.25.31.1.1.2.1', description: '华为内存设备表' }
    });

    // HP MIB
    this.addVendorMIB('hp', '*******.4.1.11', {
      'hpSystemTable': { oid: '*******.4.1.11.2.14.11.1.2', description: 'HP系统表' },
      'hpSwitchTable': { oid: '*******.4.1.11.2.14.11.5.1.7', description: 'HP交换机表' }
    });
  }

  /**
   * 添加MIB组
   */
  addMIBGroup(groupName, baseOID, objects) {
    this.mibs.set(groupName, {
      baseOID,
      objects,
      addedAt: new Date().toISOString()
    });
  }

  /**
   * 添加厂商MIB
   */
  addVendorMIB(vendor, baseOID, objects) {
    if (!this.vendors.has(vendor)) {
      this.vendors.set(vendor, new Map());
    }
    
    this.vendors.get(vendor).set(baseOID, {
      baseOID,
      objects,
      addedAt: new Date().toISOString()
    });
  }

  /**
   * 根据OID查找MIB信息
   */
  findMIBByOID(oid) {
    // 在标准MIB中查找
    for (const [groupName, group] of this.mibs) {
      for (const [objectName, objectInfo] of Object.entries(group.objects)) {
        if (objectInfo.oid === oid || oid.startsWith(objectInfo.oid)) {
          return {
            group: groupName,
            object: objectName,
            ...objectInfo,
            isStandard: true
          };
        }
      }
    }

    // 在厂商MIB中查找
    for (const [vendor, vendorMIBs] of this.vendors) {
      for (const [baseOID, mibGroup] of vendorMIBs) {
        if (oid.startsWith(baseOID)) {
          return {
            vendor,
            baseOID,
            ...mibGroup,
            isVendorSpecific: true
          };
        }
      }
    }

    return null;
  }

  /**
   * 获取设备推荐的MIB对象
   */
  getRecommendedMIBs(deviceType, vendor = null) {
    const recommended = {
      essential: [], // 必需的MIB对象
      monitoring: [], // 监控相关的MIB对象
      management: [], // 管理相关的MIB对象
      vendorSpecific: [] // 厂商特定的MIB对象
    };

    // 基础系统信息（所有设备都需要）
    recommended.essential = [
      '*******.*******.0', // sysDescr
      '*******.*******.0', // sysUpTime
      '*******.*******.0', // sysName
      '*******.*******.0'  // ifNumber
    ];

    // 根据设备类型添加推荐MIB
    switch (deviceType) {
      case 'router':
      case 'switch':
        recommended.monitoring.push(
          '*******.*******.1.8', // ifOperStatus
          '*******.*******.1.10', // ifInOctets
          '*******.*******.1.16', // ifOutOctets
          '*******.*******.0', // ipInReceives
          '*******.*******0.0' // ipOutRequests
        );
        break;
        
      case 'server':
        recommended.monitoring.push(
          '*******.********.3.1.2', // hrProcessorLoad
          '*******.********.2.0', // hrMemorySize
          '*******.********.3' // hrStorageTable
        );
        break;
    }

    // 添加厂商特定MIB
    if (vendor && this.vendors.has(vendor.toLowerCase())) {
      const vendorMIBs = this.vendors.get(vendor.toLowerCase());
      for (const [baseOID, mibInfo] of vendorMIBs) {
        recommended.vendorSpecific.push(baseOID);
      }
    }

    return recommended;
  }

  /**
   * 创建设备配置文件
   */
  createDeviceProfile(deviceInfo) {
    const profile = {
      deviceId: deviceInfo.ipAddress,
      deviceType: deviceInfo.deviceType,
      vendor: deviceInfo.vendor,
      model: deviceInfo.model || 'Unknown',
      supportedMIBs: [],
      monitoringOIDs: [],
      pollingInterval: 30000,
      createdAt: new Date().toISOString()
    };

    // 获取推荐的MIB对象
    const recommended = this.getRecommendedMIBs(deviceInfo.deviceType, deviceInfo.vendor);
    
    profile.supportedMIBs = [
      ...recommended.essential,
      ...recommended.monitoring,
      ...recommended.management
    ];

    profile.monitoringOIDs = [
      ...recommended.essential,
      ...recommended.monitoring
    ];

    // 根据设备类型调整轮询间隔
    switch (deviceInfo.deviceType) {
      case 'router':
      case 'switch':
        profile.pollingInterval = 15000; // 网络设备需要更频繁的监控
        break;
      case 'server':
        profile.pollingInterval = 30000;
        break;
      default:
        profile.pollingInterval = 60000;
    }

    this.deviceProfiles.set(deviceInfo.ipAddress, profile);
    return profile;
  }

  /**
   * 获取MIB对象的详细信息
   */
  getMIBObjectInfo(oid) {
    const mibInfo = this.findMIBByOID(oid);
    
    if (mibInfo) {
      return {
        oid,
        name: mibInfo.object || 'Unknown',
        type: mibInfo.type || 'Unknown',
        access: mibInfo.access || 'Unknown',
        description: mibInfo.description || 'No description available',
        group: mibInfo.group || mibInfo.vendor,
        isStandard: mibInfo.isStandard || false,
        isVendorSpecific: mibInfo.isVendorSpecific || false
      };
    }

    return {
      oid,
      name: 'Unknown',
      type: 'Unknown',
      access: 'Unknown',
      description: 'Unknown OID',
      group: 'Unknown',
      isStandard: false,
      isVendorSpecific: false
    };
  }

  /**
   * 验证OID格式
   */
  validateOID(oid) {
    const oidPattern = /^1(\.\d+)+$/;
    return oidPattern.test(oid);
  }

  /**
   * 格式化OID显示
   */
  formatOID(oid) {
    const mibInfo = this.findMIBByOID(oid);
    
    if (mibInfo && mibInfo.object) {
      return `${mibInfo.object} (${oid})`;
    }
    
    return oid;
  }

  /**
   * 获取所有支持的MIB组
   */
  getAllMIBGroups() {
    const groups = [];
    
    for (const [groupName, group] of this.mibs) {
      groups.push({
        name: groupName,
        baseOID: group.baseOID,
        objectCount: Object.keys(group.objects).length,
        type: 'standard'
      });
    }
    
    for (const [vendor, vendorMIBs] of this.vendors) {
      for (const [baseOID, mibGroup] of vendorMIBs) {
        groups.push({
          name: `${vendor}-${baseOID}`,
          baseOID,
          objectCount: Object.keys(mibGroup.objects || {}).length,
          type: 'vendor',
          vendor
        });
      }
    }
    
    return groups;
  }

  /**
   * 导出MIB数据库
   */
  exportDatabase() {
    return {
      mibs: Object.fromEntries(this.mibs),
      vendors: Object.fromEntries(
        Array.from(this.vendors.entries()).map(([vendor, mibMap]) => [
          vendor,
          Object.fromEntries(mibMap)
        ])
      ),
      deviceProfiles: Object.fromEntries(this.deviceProfiles),
      exportedAt: new Date().toISOString()
    };
  }

  /**
   * 导入MIB数据库
   */
  importDatabase(data) {
    if (data.mibs) {
      this.mibs = new Map(Object.entries(data.mibs));
    }
    
    if (data.vendors) {
      this.vendors = new Map(
        Object.entries(data.vendors).map(([vendor, mibData]) => [
          vendor,
          new Map(Object.entries(mibData))
        ])
      );
    }
    
    if (data.deviceProfiles) {
      this.deviceProfiles = new Map(Object.entries(data.deviceProfiles));
    }
  }
}

module.exports = MIBDatabase;
