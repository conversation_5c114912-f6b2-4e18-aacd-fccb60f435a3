// AndroidNetworkTools测试组件
import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Alert } from 'react-native';
import { Search, Wifi, CheckCircle, AlertCircle, Clock } from 'lucide-react-native';
import AndroidNetworkTools from '../modules/android-network-tools';

interface TestResult {
  test: string;
  status: 'pending' | 'success' | 'error';
  message: string;
  data?: any;
  duration?: number;
}

const NetworkToolsTest: React.FC = () => {
  const [testing, setTesting] = useState(false);
  const [results, setResults] = useState<TestResult[]>([]);

  const addResult = (result: TestResult) => {
    setResults(prev => [...prev, result]);
  };

  const updateResult = (index: number, updates: Partial<TestResult>) => {
    setResults(prev => prev.map((result, i) => 
      i === index ? { ...result, ...updates } : result
    ));
  };

  const runAllTests = async () => {
    if (testing) return;

    setTesting(true);
    setResults([]);

    const tests = [
      { name: '模块可用性检查', test: testModuleAvailability },
      { name: 'ARP子网扫描', test: testARPScan },
      { name: 'Ping测试', test: testPing },
      { name: '端口扫描', test: testPortScan },
      { name: '增强设备信息', test: testEnhancedInfo },
      { name: '完整网络扫描', test: testFullScan }
    ];

    for (const { name, test } of tests) {
      const startTime = Date.now();
      const resultIndex = results.length;
      
      addResult({
        test: name,
        status: 'pending',
        message: '测试中...'
      });

      try {
        const result = await test();
        const duration = Date.now() - startTime;
        
        updateResult(resultIndex, {
          status: 'success',
          message: result.message,
          data: result.data,
          duration
        });
      } catch (error) {
        const duration = Date.now() - startTime;
        
        updateResult(resultIndex, {
          status: 'error',
          message: error.message || '测试失败',
          duration
        });
      }

      // 测试间延迟
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    setTesting(false);
  };

  // 测试1: 模块可用性
  const testModuleAvailability = async () => {
    const isAvailable = await AndroidNetworkTools.isAvailable();
    
    if (isAvailable) {
      return {
        message: '✅ AndroidNetworkTools模块可用',
        data: { available: true }
      };
    } else {
      throw new Error('❌ AndroidNetworkTools模块不可用');
    }
  };

  // 测试2: ARP扫描
  const testARPScan = async () => {
    const devices = await AndroidNetworkTools.scanSubnetDevices();
    
    return {
      message: `✅ ARP扫描完成，发现 ${devices.length} 个设备`,
      data: { 
        deviceCount: devices.length,
        devices: devices.slice(0, 3) // 只显示前3个设备
      }
    };
  };

  // 测试3: Ping测试
  const testPing = async () => {
    // 测试网关
    const gatewayIP = '***********';
    const pingResult = await AndroidNetworkTools.pingDevice(gatewayIP);
    
    return {
      message: `✅ Ping ${gatewayIP}: ${pingResult.isReachable ? '可达' : '不可达'} (${pingResult.responseTime}ms)`,
      data: pingResult
    };
  };

  // 测试4: 端口扫描
  const testPortScan = async () => {
    const targetIP = '***********';
    const portResult = await AndroidNetworkTools.scanDevicePorts(targetIP);
    
    return {
      message: `✅ 端口扫描 ${targetIP}: 发现 ${portResult.openPorts.length} 个开放端口`,
      data: portResult
    };
  };

  // 测试5: 增强设备信息
  const testEnhancedInfo = async () => {
    const targetIP = '***********';
    const enhancedInfo = await AndroidNetworkTools.getEnhancedDeviceInfo(targetIP);
    
    return {
      message: `✅ 增强信息 ${targetIP}: ${enhancedInfo.detectedType}, SNMP: ${enhancedInfo.snmpSupported}`,
      data: enhancedInfo
    };
  };

  // 测试6: 完整网络扫描
  const testFullScan = async () => {
    const fullScanResult = await AndroidNetworkTools.performFullNetworkScan();
    
    return {
      message: `✅ 完整扫描: ${fullScanResult.scanSummary.totalDevices} 个设备，${fullScanResult.scanSummary.snmpDevices} 个支持SNMP`,
      data: fullScanResult.scanSummary
    };
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'pending':
        return <Clock size={20} color="#faad14" />;
      case 'success':
        return <CheckCircle size={20} color="#52c41a" />;
      case 'error':
        return <AlertCircle size={20} color="#ff4d4f" />;
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'pending': return '#faad14';
      case 'success': return '#52c41a';
      case 'error': return '#ff4d4f';
    }
  };

  const showResultDetails = (result: TestResult) => {
    if (result.data) {
      Alert.alert(
        `${result.test} - 详细结果`,
        JSON.stringify(result.data, null, 2),
        [{ text: '确定' }]
      );
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>AndroidNetworkTools 测试</Text>
        <TouchableOpacity
          style={[styles.testButton, testing && styles.testButtonDisabled]}
          onPress={runAllTests}
          disabled={testing}
        >
          <Search size={20} color="#fff" />
          <Text style={styles.testButtonText}>
            {testing ? '测试中...' : '开始测试'}
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.resultsList} showsVerticalScrollIndicator={false}>
        {results.map((result, index) => (
          <TouchableOpacity
            key={index}
            style={[styles.resultCard, { borderLeftColor: getStatusColor(result.status) }]}
            onPress={() => showResultDetails(result)}
          >
            <View style={styles.resultHeader}>
              <View style={styles.resultIcon}>
                {getStatusIcon(result.status)}
              </View>
              <View style={styles.resultInfo}>
                <Text style={styles.resultTitle}>{result.test}</Text>
                <Text style={styles.resultMessage}>{result.message}</Text>
              </View>
              {result.duration && (
                <View style={styles.resultDuration}>
                  <Text style={styles.durationText}>{result.duration}ms</Text>
                </View>
              )}
            </View>

            {result.data && (
              <View style={styles.resultData}>
                <Text style={styles.dataLabel}>点击查看详细数据</Text>
              </View>
            )}
          </TouchableOpacity>
        ))}

        {results.length === 0 && !testing && (
          <View style={styles.emptyState}>
            <Wifi size={48} color="#d9d9d9" />
            <Text style={styles.emptyText}>点击"开始测试"运行网络工具测试</Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e8e8e8',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  testButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#1890ff',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    gap: 8,
  },
  testButtonDisabled: {
    backgroundColor: '#d9d9d9',
  },
  testButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  resultsList: {
    flex: 1,
    padding: 16,
  },
  resultCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  resultHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  resultIcon: {
    marginRight: 12,
  },
  resultInfo: {
    flex: 1,
  },
  resultTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  resultMessage: {
    fontSize: 14,
    color: '#666',
  },
  resultDuration: {
    marginLeft: 8,
  },
  durationText: {
    fontSize: 12,
    color: '#999',
  },
  resultData: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  dataLabel: {
    fontSize: 12,
    color: '#1890ff',
    fontStyle: 'italic',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 48,
  },
  emptyText: {
    fontSize: 16,
    color: '#999',
    marginTop: 16,
    textAlign: 'center',
  },
});

export default NetworkToolsTest;
