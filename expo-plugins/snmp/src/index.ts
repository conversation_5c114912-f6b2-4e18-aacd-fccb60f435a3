// src/index.ts
import { NativeModules } from 'react-native';

const { EXSNMP } = NativeModules;

export type DeviceInfo = {
  ip: string;
  sysName: string;
  sysDescr: string;
  cpu?: number;
  memory?: number;
};

export type SNMPError = {
  code: number;
  message: string;
};

export type SNMPOID = 'sysName' | 'sysDescr' | 'cpuLoad' | 'memoryUsed' | 'memoryTotal';

export const snmpService = {
  /**
   * 扫描指定子网中的 SNMP 设备
   * @param subnet 子网地址（如 192.168.1）
   * @returns 扫描到的设备列表
   */
  discoverDevices(subnet: string): Promise<{ devices: DeviceInfo[]; } | { error: SNMPError; }> {
    return new Promise((resolve, reject) => {
      if (!EXSNMP) {
        reject({ code: -1, message: 'SNMP module not available' });
        return;
      }

      EXSNMP.discoverDevices(subnet)
        .then((result: any) => {
          resolve({ devices: result.devices });
        })
        .catch((error: any) => {
          reject({ code: error.code || -1, message: error.message || 'Unknown error' });
        });
    });
  },

  /**
   * 获取指定设备的 SNMP 数据
   * @param ip 设备IP地址
   * @param oid 要获取的数据类型
   * @returns SNMP 数据值
   */
  getDeviceValue(ip: string, oid: SNMPOID): Promise<{ value: string; } | { error: SNMPError; }> {
    return new Promise((resolve, reject) => {
      if (!EXSNMP) {
        reject({ code: -1, message: 'SNMP module not available' });
        return;
      }

      EXSNMP.getDeviceValue(ip, oid)
        .then((result: any) => {
          resolve({ value: result.value });
        })
        .catch((error: any) => {
          reject({ code: error.code || -1, message: error.message || 'Unknown error' });
        });
    });
  },
};

export default snmpService;