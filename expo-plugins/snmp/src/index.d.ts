export type DeviceInfo = {
  ip: string;
  sysName: string;
  sysDescr: string;
  cpu?: number;
  memory?: number;
};

export type SNMPError = {
  code: number;
  message: string;
};

export type SNMPOID = 'sysName' | 'sysDescr' | 'cpuLoad' | 'memoryUsed' | 'memoryTotal';

export interface SNMPService {
  discoverDevices(subnet: string): Promise<{ devices: DeviceInfo[]; } | { error: SNMPError; }>;
  getDeviceValue(ip: string, oid: SNMPOID): Promise<{ value: string; } | { error: SNMPError; }>;
}

declare const snmpService: SNMPService;
export { snmpService };
export default snmpService;
