apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

android {
    namespace 'com.example.smartdm'
    compileSdk 34

    defaultConfig {
        minSdk 21
        targetSdk 34
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }
}

repositories {
    google()
    mavenCentral()
    maven { url 'https://jitpack.io' }
}

dependencies {
    implementation 'com.facebook.react:react-native:+'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3'
    // 使用一个简单的 SNMP 库进行测试
    implementation 'org.snmp4j:snmp4j:3.7.7'
}