// android/src/main/java/com/example/smartdm/SNMPModule.kt
package com.example.smartdm

import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod
import com.facebook.react.bridge.Promise
import com.facebook.react.bridge.WritableMap
import com.facebook.react.bridge.WritableArray
import com.facebook.react.bridge.Arguments
import kotlinx.coroutines.*
import java.net.InetAddress
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import org.snmp4j.*
import org.snmp4j.mp.SnmpConstants
import org.snmp4j.smi.*
import org.snmp4j.transport.DefaultUdpTransportMapping
import java.io.IOException

class SNMPModule(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {

    private val executor: ExecutorService = Executors.newSingleThreadExecutor()
    private val scope = MainScope()

    override fun getName(): String {
        return "EXSNMP"
    }

    @ReactMethod
    fun discoverDevices(subnet: String, promise: Promise) {
        scope.launch(Dispatchers.IO) {
            try {
                val devices = Arguments.createArray()

                // 为了演示，我们先扫描几个常见的IP地址
                val testIps = listOf("$subnet.1", "$subnet.2", "$subnet.100", "$subnet.254")

                for (ip in testIps) {
                    try {
                        // 首先检查主机是否可达
                        val address = InetAddress.getByName(ip)
                        if (address.isReachable(1000)) { // 1秒超时
                            // 尝试SNMP查询
                            val sysName = snmpGet(ip, "*******.*******.0") // sysName OID
                            val sysDescr = snmpGet(ip, "*******.*******.0") // sysDescr OID

                            if (sysName != null || sysDescr != null) {
                                val device = Arguments.createMap().apply {
                                    putString("ip", ip)
                                    putString("sysName", sysName ?: "Unknown Device")
                                    putString("sysDescr", sysDescr ?: "Network Device")
                                }
                                devices.pushMap(device)
                            }
                        }
                    } catch (e: Exception) {
                        // 忽略单个IP的错误，继续扫描其他IP
                        continue
                    }
                }

                val result = Arguments.createMap().apply {
                    putArray("devices", devices)
                }

                withContext(Dispatchers.Main) {
                    promise.resolve(result)
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    promise.reject("SNMP_ERROR", e.message, e)
                }
            }
        }
    }

    @ReactMethod
    fun getDeviceValue(ip: String, oid: String, promise: Promise) {
        scope.launch(Dispatchers.IO) {
            try {
                // 使用 SNMP 获取指定 OID 的值
                val result = snmpGet(ip, oid)
                
                val resultMap = Arguments.createMap().apply {
                    putString("value", result?.toString() ?: "")
                }
                
                withContext(Dispatchers.Main) {
                    promise.resolve(resultMap)
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    promise.reject("SNMP_ERROR", e.message, e)
                }
            }
        }
    }

    // 实现真实的 SNMP GET 请求
    private suspend fun snmpGet(ip: String, oid: String): String? {
        return withContext(Dispatchers.IO) {
            try {
                val transport = DefaultUdpTransportMapping()
                val snmp = Snmp(transport)
                transport.listen()

                val target = CommunityTarget().apply {
                    community = OctetString("public")
                    address = GenericAddress.parse("udp:$ip/161")
                    retries = 2
                    timeout = 1500
                    version = SnmpConstants.version2c
                }

                val pdu = PDU().apply {
                    add(VariableBinding(OID(oid)))
                    type = PDU.GET
                }

                val response = snmp.send(pdu, target)
                snmp.close()

                if (response?.response != null) {
                    val variable = response.response.get(0).variable
                    if (variable != null && !variable.isException) {
                        variable.toString()
                    } else {
                        null
                    }
                } else {
                    null
                }
            } catch (e: Exception) {
                null
            }
        }
    }
}