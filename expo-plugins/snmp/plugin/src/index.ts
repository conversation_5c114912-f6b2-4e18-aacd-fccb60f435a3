import { ConfigPlugin, withPlugins } from '@expo/config-plugins';
import { withAndroidMainApplication } from '@expo/config-plugins/build/android';
import { withIosAppDelegate } from '@expo/config-plugins/build/ios';

const withSNMP: ConfigPlugin = (config) => {
  return withPlugins(config, [
    // Android configuration
    [
      withAndroidMainApplication,
      (config) => {
        const mainApplication = config.modResults;
        
        // Add import for SNMP package
        if (!mainApplication.contents.includes('import com.example.smartdm.SNMPPackage;')) {
          mainApplication.contents = mainApplication.contents.replace(
            /import com\.facebook\.react\.ReactApplication;/,
            `import com.facebook.react.ReactApplication;
import com.example.smartdm.SNMPPackage;`
          );
        }
        
        // Add SNMP package to the list
        if (!mainApplication.contents.includes('new SNMPPackage()')) {
          mainApplication.contents = mainApplication.contents.replace(
            /return Arrays\.asList\(/,
            `return Arrays.asList(
          new SNMPPackage(),`
          );
        }
        
        return config;
      },
    ],
    // iOS configuration would go here if needed
  ]);
};

export default withSNMP;
