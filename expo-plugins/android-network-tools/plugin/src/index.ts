// Expo插件配置 - AndroidNetworkTools
import { ConfigPlugin, withDangerousMod, withPlugins } from '@expo/config-plugins';
import { ExpoConfig } from '@expo/config-types';
import * as fs from 'fs';
import * as path from 'path';

const withAndroidNetworkTools: ConfigPlugin = (config) => {
  return withPlugins(config, [
    // 添加Android依赖
    withAndroidDependencies,
    // 注册原生模块
    withAndroidMainApplication,
  ]);
};

const withAndroidDependencies: ConfigPlugin = (config) => {
  return withDangerousMod(config, [
    'android',
    async (config) => {
      const buildGradlePath = path.join(config.modRequest.platformProjectRoot, 'app', 'build.gradle');
      
      if (fs.existsSync(buildGradlePath)) {
        let buildGradleContent = fs.readFileSync(buildGradlePath, 'utf8');
        
        // 添加依赖
        const dependencies = `
    implementation 'com.github.stealthcopter:AndroidNetworkTools:0.4.5.3'
    implementation 'org.snmp4j:snmp4j:3.7.7'
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'`;
        
        if (!buildGradleContent.includes('AndroidNetworkTools')) {
          buildGradleContent = buildGradleContent.replace(
            /dependencies\s*{/,
            `dependencies {${dependencies}`
          );
          
          fs.writeFileSync(buildGradlePath, buildGradleContent);
        }
      }
      
      return config;
    },
  ]);
};

const withAndroidMainApplication: ConfigPlugin = (config) => {
  return withDangerousMod(config, [
    'android',
    async (config) => {
      const mainApplicationPath = path.join(
        config.modRequest.platformProjectRoot,
        'app',
        'src',
        'main',
        'java',
        'com',
        'example',
        'smartdm',
        'MainApplication.java'
      );
      
      if (fs.existsSync(mainApplicationPath)) {
        let mainApplicationContent = fs.readFileSync(mainApplicationPath, 'utf8');
        
        // 添加import
        const importStatement = 'import com.smartdm.networktools.NetworkToolsPackage;';
        if (!mainApplicationContent.includes(importStatement)) {
          mainApplicationContent = mainApplicationContent.replace(
            /import com\.facebook\.react\.ReactApplication;/,
            `import com.facebook.react.ReactApplication;\n${importStatement}`
          );
        }
        
        // 添加包到getPackages方法
        const packageStatement = 'new NetworkToolsPackage(),';
        if (!mainApplicationContent.includes(packageStatement)) {
          mainApplicationContent = mainApplicationContent.replace(
            /new MainReactPackage\(\),/,
            `new MainReactPackage(),\n          ${packageStatement}`
          );
        }
        
        fs.writeFileSync(mainApplicationPath, mainApplicationContent);
      }
      
      return config;
    },
  ]);
};

export default withAndroidNetworkTools;
