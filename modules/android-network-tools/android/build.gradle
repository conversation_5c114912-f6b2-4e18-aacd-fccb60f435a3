apply plugin: 'com.android.library'

android {
    compileSdkVersion 34
    
    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 34
        versionCode 1
        versionName "1.0"
    }
    
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

repositories {
    google()
    mavenCentral()
    maven { url 'https://jitpack.io' }
}

dependencies {
    implementation 'com.facebook.react:react-native:+'
    implementation 'com.github.stealthcopter:AndroidNetworkTools:0.4.5.3'
    implementation 'org.snmp4j:snmp4j:3.7.7'
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
}
