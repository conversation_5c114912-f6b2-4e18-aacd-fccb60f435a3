// AndroidNetworkTools React Native接口
import { NativeModules, Platform } from 'react-native';

const { AndroidNetworkTools } = NativeModules;

export interface DeviceInfo {
  ip: string;
  hostname: string;
  mac: string;
  responseTime: number;
  discoveryMethod: 'ARP' | 'HTTP' | 'PING';
  vendor: string;
  type: string;
}

export interface PingResult {
  ip: string;
  isReachable: boolean;
  responseTime: number;
  error?: string;
}

export interface PortScanResult {
  ip: string;
  openPorts: number[];
  services: Array<{
    port: number;
    service: string;
  }>;
  warning?: string;
}

export interface EnhancedDeviceInfo {
  ip: string;
  scanTime: string;
  isReachable: boolean;
  responseTime?: number;
  openPorts: number[];
  services: Array<{
    port: number;
    service: string;
  }>;
  snmpSupported: boolean;
  detectedType: string;
  error?: string;
  warning?: string;
}

/**
 * AndroidNetworkTools包装类
 */
class AndroidNetworkToolsWrapper {
  
  /**
   * 检查模块是否可用
   */
  async isAvailable(): Promise<boolean> {
    if (Platform.OS !== 'android') {
      console.warn('AndroidNetworkTools只支持Android平台');
      return false;
    }
    
    if (!AndroidNetworkTools) {
      console.warn('AndroidNetworkTools原生模块未找到');
      return false;
    }
    
    try {
      return await AndroidNetworkTools.isAvailable();
    } catch (error) {
      console.error('AndroidNetworkTools可用性检查失败:', error);
      return false;
    }
  }
  
  /**
   * ARP扫描发现局域网设备
   */
  async scanSubnetDevices(): Promise<DeviceInfo[]> {
    if (!await this.isAvailable()) {
      throw new Error('AndroidNetworkTools不可用');
    }
    
    try {
      console.log('🔍 开始ARP扫描...');
      const devices = await AndroidNetworkTools.scanSubnetDevices();
      console.log(`✅ ARP扫描完成，发现 ${devices.length} 个设备`);
      return devices;
    } catch (error) {
      console.error('❌ ARP扫描失败:', error);
      throw error;
    }
  }
  
  /**
   * Ping测试单个设备
   */
  async pingDevice(ipAddress: string): Promise<PingResult> {
    if (!await this.isAvailable()) {
      throw new Error('AndroidNetworkTools不可用');
    }
    
    try {
      console.log(`🏓 Ping测试: ${ipAddress}`);
      const result = await AndroidNetworkTools.pingDevice(ipAddress);
      console.log(`📊 Ping结果 ${ipAddress}: ${result.isReachable ? '可达' : '不可达'} (${result.responseTime}ms)`);
      return result;
    } catch (error) {
      console.error(`❌ Ping测试失败 ${ipAddress}:`, error);
      throw error;
    }
  }
  
  /**
   * 扫描设备开放端口
   */
  async scanDevicePorts(ipAddress: string): Promise<PortScanResult> {
    if (!await this.isAvailable()) {
      throw new Error('AndroidNetworkTools不可用');
    }
    
    try {
      console.log(`🔌 端口扫描: ${ipAddress}`);
      const result = await AndroidNetworkTools.scanDevicePorts(ipAddress);
      console.log(`📊 端口扫描结果 ${ipAddress}: ${result.openPorts.length} 个开放端口`);
      return result;
    } catch (error) {
      console.error(`❌ 端口扫描失败 ${ipAddress}:`, error);
      throw error;
    }
  }
  
  /**
   * 获取增强的设备信息
   */
  async getEnhancedDeviceInfo(ipAddress: string): Promise<EnhancedDeviceInfo> {
    if (!await this.isAvailable()) {
      throw new Error('AndroidNetworkTools不可用');
    }
    
    try {
      console.log(`🔍 获取增强设备信息: ${ipAddress}`);
      const result = await AndroidNetworkTools.getEnhancedDeviceInfo(ipAddress);
      console.log(`📊 增强信息获取完成 ${ipAddress}: ${result.detectedType}, SNMP: ${result.snmpSupported}`);
      return result;
    } catch (error) {
      console.error(`❌ 获取增强设备信息失败 ${ipAddress}:`, error);
      throw error;
    }
  }
  
  /**
   * 批量获取设备增强信息
   */
  async batchGetEnhancedInfo(ipAddresses: string[]): Promise<EnhancedDeviceInfo[]> {
    console.log(`🔍 批量获取 ${ipAddresses.length} 个设备的增强信息`);
    
    const results: EnhancedDeviceInfo[] = [];
    const batchSize = 5; // 并发限制
    
    for (let i = 0; i < ipAddresses.length; i += batchSize) {
      const batch = ipAddresses.slice(i, i + batchSize);
      
      const batchPromises = batch.map(async (ip) => {
        try {
          return await this.getEnhancedDeviceInfo(ip);
        } catch (error) {
          console.warn(`获取设备信息失败 ${ip}:`, error);
          return {
            ip,
            scanTime: new Date().toISOString(),
            isReachable: false,
            openPorts: [],
            services: [],
            snmpSupported: false,
            detectedType: 'unknown',
            error: error.message
          };
        }
      });
      
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
      
      // 批次间延迟，避免网络拥塞
      if (i + batchSize < ipAddresses.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    console.log(`✅ 批量增强信息获取完成: ${results.length} 个设备`);
    return results;
  }
  
  /**
   * 完整的网络扫描流程
   */
  async performFullNetworkScan(): Promise<{
    discoveredDevices: DeviceInfo[];
    enhancedDevices: EnhancedDeviceInfo[];
    scanSummary: {
      totalDevices: number;
      reachableDevices: number;
      snmpDevices: number;
      routerDevices: number;
      printerDevices: number;
      unknownDevices: number;
    };
  }> {
    console.log('🚀 开始完整网络扫描...');
    
    try {
      // 阶段1: ARP发现
      const discoveredDevices = await this.scanSubnetDevices();
      
      if (discoveredDevices.length === 0) {
        console.warn('⚠️ 未发现任何设备');
        return {
          discoveredDevices: [],
          enhancedDevices: [],
          scanSummary: {
            totalDevices: 0,
            reachableDevices: 0,
            snmpDevices: 0,
            routerDevices: 0,
            printerDevices: 0,
            unknownDevices: 0
          }
        };
      }
      
      // 阶段2: 增强信息获取
      const ipAddresses = discoveredDevices.map(device => device.ip);
      const enhancedDevices = await this.batchGetEnhancedInfo(ipAddresses);
      
      // 生成扫描摘要
      const scanSummary = {
        totalDevices: enhancedDevices.length,
        reachableDevices: enhancedDevices.filter(d => d.isReachable).length,
        snmpDevices: enhancedDevices.filter(d => d.snmpSupported).length,
        routerDevices: enhancedDevices.filter(d => d.detectedType === 'router').length,
        printerDevices: enhancedDevices.filter(d => d.detectedType === 'printer').length,
        unknownDevices: enhancedDevices.filter(d => d.detectedType === 'unknown').length
      };
      
      console.log('✅ 完整网络扫描完成:', scanSummary);
      
      return {
        discoveredDevices,
        enhancedDevices,
        scanSummary
      };
      
    } catch (error) {
      console.error('❌ 完整网络扫描失败:', error);
      throw error;
    }
  }
}

export default new AndroidNetworkToolsWrapper();
